using AutoMapper;
using Gateway.Common.Results;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.EnumUtilities.Validation;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Addresses;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Models.Mediator;
using WorkTimeApi.Services.Interfaces.Companies;
using WorkTimeApi.Validators.Interfaces;

namespace WorkTimeApi.Services.Companies
{
    public class CompaniesService(IMapper mapper,
        ICompaniesRepository companiesRepository,
        ICompanyValidator companyValidator,
        IEmployeesRepository employeesRepository,
        IAddressesRepository addressesRepository,
        IMediator mediator) : ICompaniesService
    {
        public async Task<GetCompaniesResponse> GetCompaniesAsync(Guid userId)
        {
            var activeCompanies = await companiesRepository.GetActiveCompaniesAsync(userId);

            var pendingCompanies = await companiesRepository.GetPendingCompaniesAsync(userId);

            return new GetCompaniesResponse
            {
                ActiveCompanies = mapper.Map<List<CompanyDTO>>(activeCompanies),
                PendingCompanies = mapper.Map<List<CompanyDTO>>(pendingCompanies)
            };
        }

        public async Task<CompanyDTO> GetCompanyAsync(Guid id)
        {
            var company = await companiesRepository.GetCompanyAsync(id);

            return mapper.Map<CompanyDTO>(company);
        }

        public async Task<Result<CompanyDTO>> CreateCompanyAsync(CompanyDTO companyDTO, Guid userId)
        {
            var company = mapper.Map<Company>(companyDTO);
            var addresses = mapper.Map<IEnumerable<Address>>(companyDTO.Addresses);

            var validationResult = await companyValidator.ValidateCompanyAsync(company);
            if (validationResult.HasValidationErrors)
                return validationResult;

            company = await companiesRepository.CreateCompanyAsync(company, userId, EmployeeStatus.Active);
            var companyEmployee = company.Employees.FirstOrDefault(e => e.UserId == userId);
            if (companyEmployee != null)
                mediator.Publish(new AddDefaultNotificationSettings(companyEmployee.Id, new Guid(DefaultWorktimeRole.Creator)));

            if (addresses is not null && addresses.Any())
            {
                var companyAddresses = new List<CompanyAddress>();
                foreach (var address in addresses)
                {
                    if (address is null)
                        continue;

                    var city = await addressesRepository.GetCityByNameAndPostalCodeAsync(address.City?.Name, address.PostalCode);
                    address.CityId = city?.Id;
                    address.CityName = address.City?.Name;
                    address.City = null;

                    var district = await addressesRepository.GetDistrictByNameAsync(address.District?.Name);
                    address.DistrictId = district?.Id;
                    address.DistrictName = address.District?.Name;
                    address.District = null;

                    var municipality = await addressesRepository.GetMunicipalityByNameAsync(address.Municipality?.Name);
                    address.MunicipalityId = municipality?.Id;
                    address.MunicipalityName = address.Municipality?.Name;
                    address.Municipality = null;

                    await addressesRepository.AddAddressAsync(address);
                    var companyAddress = new CompanyAddress() { CompanyId = company.Id, AddressId = address.Id };
                    companyAddresses.Add(companyAddress);
                }

                await companiesRepository.AddCompanyAddressesAsync(companyAddresses);
            }

            if (companyDTO.Kids is not null && companyDTO.Kids.Count > 0)
            {
                var companyKids = new List<CompanyKid>();
                foreach (var kid in companyDTO.Kids)
                {
                    if (kid is null || kid.Id == Guid.Empty)
                        continue;

                    companyKids.Add(new CompanyKid() { CompanyId = company.Id, KidId = kid.Id });
                }

                await companiesRepository.AddCompanyKidsAsync(companyKids);
            }

            return mapper.Map<CompanyDTO>(company);
        }

        public async Task<Result<CompanyDTO>> UpdateCompanyAsync(Guid id, CompanyDTO companyDTO)
        {
            var existingCompany = await GetCompanyAsync(id);
            var addesses = mapper.Map<IEnumerable<Address>>(companyDTO.Addresses);

            if (existingCompany is null)
                return new ValidationResult([(int)CompanyValidationErrorsEnum.CompanyAlreadyExists]);

            existingCompany.Name = companyDTO.Name;
            existingCompany.Bulstat = companyDTO.Bulstat;
            existingCompany.ContactName = companyDTO.ContactName ?? existingCompany.ContactName;
            existingCompany.EKATTECode = companyDTO.EKATTECode ?? existingCompany.EKATTECode;
            existingCompany.VacationDays = companyDTO.VacationDays;

            await companiesRepository.UpdateCompanyAsync(mapper.Map<Company>(existingCompany));
            await companiesRepository.RemoveCompanyAddressesAsync(id);
            await companiesRepository.RemoveCompanyKidsAsync(id);

            if (addesses is not null && addesses.Any())
            {
                var companyAddresses = new List<CompanyAddress>();
                foreach (var address in addesses)
                {
                    if (address is null)
                        continue;

                    var city = await addressesRepository.GetCityByNameAndPostalCodeAsync(address.City?.Name, address.PostalCode);
                    address.CityId = city?.Id;
                    address.CityName = address.City?.Name;
                    address.City = null;

                    var district = await addressesRepository.GetDistrictByNameAsync(address.District?.Name);
                    address.DistrictId = district?.Id;
                    address.DistrictName = address.District?.Name;
                    address.District = null;

                    var municipality = await addressesRepository.GetMunicipalityByNameAsync(address.Municipality?.Name);
                    address.MunicipalityId = municipality?.Id;
                    address.MunicipalityName = address.Municipality?.Name;
                    address.Municipality = null;

                    await addressesRepository.AddAddressAsync(address);
                    var companyAddress = new CompanyAddress() { CompanyId = id, AddressId = address.Id };
                    companyAddresses.Add(companyAddress);
                }

                await companiesRepository.AddCompanyAddressesAsync(companyAddresses);
            }

            if (companyDTO.Kids is not null && companyDTO.Kids.Count > 0)
            {
                var companyKids = new List<CompanyKid>();
                foreach (var kid in companyDTO.Kids)
                {
                    if (kid is null || kid.Id == Guid.Empty)
                        continue;

                    companyKids.Add(new CompanyKid() { CompanyId = id, KidId = kid.Id });
                }

                await companiesRepository.AddCompanyKidsAsync(companyKids);
            }

            return existingCompany;
        }

        public async Task<CompanyDTO> EditCompanyAsync(EditCompanyRequest request)
        {
            Company? company = null;

            if (request.Id != Guid.Empty)
            {
                company = await companiesRepository.GetCompanyAsync(request.Id);
            }
            else if (request.UserRegistrationCompanyId > 0)
            {
                company = await companiesRepository.GetCompanyByUserRegistrarionsIdAsync(request.UserRegistrationCompanyId);
            }

            if (company is null)
            {
                return null;
            }

            company.Name = request.Name;
            company.Bulstat = request.Bulstat;
            company.ContactName = request.ContactName;

            await companiesRepository.UpdateCompanyAsync(company);

            return mapper.Map<CompanyDTO>(company);
        }

        public async Task<CompanyDTO> ShareCompanyAsync(ShareCompanyRequest request)
        {
            var company = await companiesRepository.GetCompanyAsync(request.CompanyId);

            if (request.User is not null)
            {
                var user = new User
                {
                    Id = request.UserId,
                    FirstName = request.User.FirstName,
                    SecondName = request.User.SecondName,
                    LastName = request.User.LastName,
                    Email = request.Email,
                    HasSignedIn = true
                };

                var newEmployee = new Employee
                {
                    FirstName = request.User.FirstName,
                    SecondName = request.User.SecondName,
                    LastName = request.User.LastName,
                    Email = request.Email,
                    CompanyId = request.CompanyId
                };

                var employee = await companiesRepository.ShareCompanyAsync(user, newEmployee, request.CompanyId, request.RoleId);
                mediator.Publish(new AddDefaultNotificationSettings(employee.Id, request.RoleId));

            }
            else
            {
                await employeesRepository.AddPendingRoleEmployeeAsync(new PendingRoleEmployee
                {
                    Email = request.Email,
                    RoleId = request.RoleId,
                    CompanyId = request.CompanyId
                });
            }

            return mapper.Map<CompanyDTO>(company);
        }

        public async Task<CompanyDTO> GetCompanyAsync(string email, string bulstat)
        {
            var company = await companiesRepository.GetCompanyAsync(email, bulstat);

            return mapper.Map<CompanyDTO>(company);
        }

        public async Task DeleteCompanyAsync(Guid companyId)
        {
            await companiesRepository.DeleteCompanyAsync(companyId);
        }
    }
}
