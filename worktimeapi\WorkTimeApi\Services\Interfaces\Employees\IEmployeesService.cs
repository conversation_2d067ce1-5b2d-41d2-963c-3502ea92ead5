using Gateway.Common.Results;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.Roles;

namespace WorkTimeApi.Services.Interfaces.Employees
{
    public interface IEmployeesService
    {
        Task<Result<EmployeeDTO>> AddEmployeeAsync(EmployeeDTO addEmployeeRequest);

        Task<EmployeeDTO> AddEmployeeToCompanyAsync(EmployeeDTO employeeDTO, Guid companyId);

        Task<EmployeeDTO> GetEmployeeAsync(Guid id);

        Task<IEnumerable<EmployeeDTO>> LoadEmployeesAsync(LoadEmployeesRequest loadEmployeeRequest);

        Task<PersonalInformationDTO> LoadEmployeeAsync(LoadEmployeeRequest loadEmployeeRequest);

        Task<EmployeeDTO> EditEmployeeAsync(EditEmployeeRequest editEmployeeRequest, Guid editorEmployeeId, bool isEditorAdmin);

        Task ImportEmployeeCompaniesAsync(IEnumerable<Guid> employeeIds, Guid companyId);

        Task<List<EmployeeDTO>> GetEmployeesAsync(GetEmployeesByCompanyIdAndPermissionRequest request);

        Task<EmployeeDTO> GetUserEmployeeAsync(Guid userId, Guid companyId);

        Task<Result<EmployeeDTO>> AddEmployeeToCompanyAsync(Guid companyId, EmployeeStatus status, Guid userId);

        Task<UserEmployeePermissionsDTO> GetUserEmployeePermissionsAsync(Guid userId, Guid companyId);

        Task<PersonalInformationDTO> GetUserEmployeePersonalDataAsync(Guid userId, Guid companyId);

        Task<EmployeeDTO> DoesEmployeeExistInCompanyAsync(string email, string egn, Guid companyId);

        Task DeleteEmployeePropertyEditsAsync(Guid employeeId);

        Task DeleteEmployeePropertyEditAsync(Guid propertyEditId);

        Task ApproveEmployeePropertyEditAsync(ApproveEmployeePropertyEditRequest request);

        Task DeclineEmployeePropertyEditAsync(DeclineEmployeePropertyEditRequest request);

        Task<EmployeeDTO> GetPayrollEmployeeAsync(Guid payrollId);

        Task UpdateEmployeeStatusAsync(Guid employeeId, EmployeeStatus status);
    }
}
