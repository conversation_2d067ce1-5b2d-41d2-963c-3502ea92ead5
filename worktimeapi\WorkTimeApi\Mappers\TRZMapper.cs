﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.DTOs.Addresses;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.DTOs.TRZ;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Mappers.Interfaces;
using WorkTimeApi.Services.Interfaces.Nomenclatures;

namespace WorkTimeApi.Mappers
{
    public class TRZMapper(IMapper mapper, INKPDService nkpdService, IKidsService kidsService) : ITRZMapper
    {
        private static readonly HashSet<string> _civilContractStandartCodes = ["30714"];
        private static readonly HashSet<string> _civilContractLandlordsCodes = ["401", "402", "804", "820"];
        private static readonly HashSet<string> _civilContractDividendsCodes = ["604", "814", "8141", "913"];

        public async Task<List<EmployeeDTO>> MapTRZEmployeesDTOToEmployeesDTO(IEnumerable<TRZEmployeeDTO> trzEmployees, Guid companyId)
        {
            var employeesDTO = new List<EmployeeDTO>();

            foreach (var trzEmployee in trzEmployees)
            {
                var employeeDTO = mapper.Map<EmployeeDTO>(trzEmployee);

                var nameParts = trzEmployee?.Name?.Split(' ') ?? Array.Empty<string>();

                switch (nameParts.Length)
                {
                    case 0:
                        employeeDTO.FirstName = null;
                        employeeDTO.SecondName = null;
                        employeeDTO.LastName = null;
                        break;
                    case 1:
                        employeeDTO.FirstName = nameParts[0];
                        employeeDTO.SecondName = string.Empty;
                        employeeDTO.LastName = string.Empty;
                        break;
                    case 2:
                        employeeDTO.FirstName = nameParts[0];
                        employeeDTO.SecondName = string.Empty;
                        employeeDTO.LastName = nameParts[1];
                        break;
                    case 3:
                        employeeDTO.FirstName = nameParts[0];
                        employeeDTO.SecondName = nameParts[1];
                        employeeDTO.LastName = nameParts[2];
                        break;
                    default:
                        employeeDTO.FirstName = nameParts[0];
                        employeeDTO.SecondName = nameParts[1];
                        employeeDTO.LastName = string.Join(" ", nameParts.Skip(2));
                        break;
                }

                employeeDTO.BirthPlace = string.Empty;
                employeeDTO.WorkTimeId = trzEmployee!.WorkTimeId ?? Guid.Empty;
                employeeDTO.UploadDate = DateTime.Now;
                employeeDTO.CompanyId = companyId;
                employeeDTO.Status = EmployeeStatus.PendingTRZ;
                employeeDTO.Addresses = new List<AddressDTO>();
                employeeDTO.Phone = trzEmployee.Phone;
                employeeDTO.UserId = trzEmployee.UserId ?? Guid.Empty;
                employeeDTO.Citizenship = trzEmployee.IsForeigner == false ? "Българско" : string.Empty;

                foreach (var address in trzEmployee?.Addresses)
                {
                    var addressDTO = mapper.Map<AddressDTO>(address);
                    addressDTO.Country = Enum.TryParse(address?.CountryCode, out Country country) ? country : Country.BG;
                    addressDTO.Street = address.Location;
                    addressDTO.Municipality = new MunicipalityDTO() { Name = address.Municipality };
                    addressDTO.MunicipalityName = address.Municipality;
                    addressDTO.City = new CityDTO() { Name = address.City };
                    addressDTO.CityName = address.City;
                    addressDTO.District = new DistrictDTO() { Name = address.District };
                    addressDTO.DistrictName = address.District;
                    addressDTO.Phone = employeeDTO.Phone;
                    addressDTO.Email = employeeDTO.Email;

                    employeeDTO.Addresses.Add(addressDTO);
                }

                if (trzEmployee?.LengthsOfService != null)
                    employeeDTO.LengthsOfService = mapper.Map<List<LengthOfServiceDTO>>(trzEmployee.LengthsOfService);

                if (trzEmployee?.PendingPayrolls != null)
                    employeeDTO.PendingPayrolls = await MapTRZPayrollsToPayrollsDTO(employeeDTO, trzEmployee.PendingPayrolls, companyId);

                employeesDTO.Add(employeeDTO);
            }

            return employeesDTO;
        }

        private async Task<List<PayrollDTO>> MapTRZPayrollsToPayrollsDTO(EmployeeDTO employee, IEnumerable<TRZPayrollDTO> trzPayrolls, Guid companyId)
        {
            var payrolls = new List<PayrollDTO>();

            foreach (var trzPayroll in trzPayrolls.OrderBy(a => a.AnnexChanges))
            {
                if (trzPayroll.AnnexChanges == 0)
                {
                    var events = trzPayrolls.Where(p => p.AnnexPayrollId == trzPayroll.AnnexPayrollId).SelectMany(e => e.PendingEvents);
                    events ??= [];
                    var payroll = await MapTRZPayrollToPayrollDTO(employee, trzPayroll, companyId);

                    payroll.PendingEvents = [.. mapper.Map<List<EventDTO>>(events)];
                    payrolls.Add(payroll);
                }
                else
                {
                    var annexPayroll = await MapTRZAnnexPayrollToAnnexPayrollDTO(trzPayroll, companyId);
                    var mainTRZPayroll = trzPayrolls.FirstOrDefault(p => p.TRZPayrollId == trzPayroll.AnnexPayrollId && p.AnnexChanges == 0);

                    if (mainTRZPayroll == null)
                        continue;

                    var mainPayroll = payrolls.FirstOrDefault(p => p.TRZPayrollId == mainTRZPayroll.TRZPayrollId);
                    if (mainPayroll == null)
                        continue;

                    mainPayroll.AnnexPayrolls.Add(annexPayroll);
                }
            }

            return payrolls;
        }

        private async Task<PayrollDTO> MapTRZPayrollToPayrollDTO(EmployeeDTO trzEmployee, TRZPayrollDTO trzPayroll, Guid companyId)
        {
            var payroll = mapper.Map<PayrollDTO>(trzPayroll);

            payroll.PayrollCategory = payroll?.Contract?.Identifier == "2"
                ? PayrollCategories.ExternalServices
                : PayrollCategories.Employees;
            if (trzPayroll.Contract != null)
                payroll.ContractType = GetContractType(trzPayroll.Contract, trzPayroll.IncomeType);

            payroll.EmployeeGuid = trzEmployee.WorkTimeId;
            payroll.CompanyId = companyId;
            payroll.UploadDate = DateTime.Now;
            payroll.StructureLevelId = trzPayroll.DepartmentGroup.WorkTimeId ?? Guid.Empty;

            payroll.Kids.AddRange(await MapKids(trzPayroll.KIDs));
            payroll.Kpds.AddRange(await MapKpds(trzPayroll.KPDs));

            return payroll;
        }

        public async Task<IEnumerable<KidDTO>> MapKids(ICollection<TRZKidDTO> kids)
        {
            var kidsDTO = new List<KidDTO>();

            if (kids is null || !kids.Any())
                return kidsDTO;

            foreach (var kid in kids)
            {
                var kidDTO = await kidsService.GetKidAsync(kid.Sector, kid.Section, kid.Code, kid.KIDVersion.ValidFrom);

                if (kidDTO is null)
                    continue;

                kidsDTO.Add(kidDTO);
            }

            return kidsDTO;
        }

        public async Task<IEnumerable<KpdDTO>> MapKpds(ICollection<TRZKpdDTO> kpds)
        {
            var kpdsDTO = new List<KpdDTO>();

            if (kpds is null || !kpds.Any())
                return kpdsDTO;

            foreach (var kpd in kpds)
            {
                var kpdDTO = await nkpdService.GetKpdAsync(kpd.Section, kpd.Code, kpd.KPDVersion.ValidFrom);

                if (kpdDTO is null)
                    continue;

                kpdsDTO.Add(kpdDTO);
            }

            return kpdsDTO;
        }

        private async Task<AnnexPayrollDTO> MapTRZAnnexPayrollToAnnexPayrollDTO(TRZPayrollDTO trzAnnexPayroll, Guid companyId)
        {
            var annex = mapper.Map<AnnexPayrollDTO>(trzAnnexPayroll);

            if (trzAnnexPayroll.Contract != null)
                annex.ContractType = GetContractType(trzAnnexPayroll.Contract, trzAnnexPayroll.IncomeType);

            annex.UploadDate = DateTime.Now;
            annex.CompanyId = companyId;
            annex.StructureLevelId = trzAnnexPayroll.DepartmentGroup.WorkTimeId ?? Guid.Empty;
            annex.Kids.AddRange(await MapKids(trzAnnexPayroll.KIDs));
            annex.Kpds.AddRange(await MapKpds(trzAnnexPayroll.KPDs));

            return annex;
        }

        public async Task<CompanyDTO> MapImportCompanyRequestToCompanyDTOAsync(ImportCompanyRequest request)
        {
            var companyDTO = new CompanyDTO()
            {
                Name = request.Name,
                Bulstat = request.Bulstat,
                ContactName = request.MOL,
                EKATTECode = request.EKATTECode,
                UserRegistrationCompanyId = request.UserRegistrationsId,
                VacationDays = request.VacationDays
            };

            var registrationAddress = request.Addresses?.FirstOrDefault(a => a.Purpose?.Identifier == 2);

            if (registrationAddress is not null)
            {
                companyDTO.Addresses =
                [
                    new()
                    {
                        Country = Enum.TryParse(registrationAddress.CountryCode, out Country country) ? country : Country.BG,
                        PostalCode = registrationAddress.PostalCode,
                        Street = registrationAddress.Location,
                        Phone = registrationAddress.Phone,
                        Municipality = new MunicipalityDTO(){ Name = registrationAddress.Municipality },
                        City = new CityDTO(){ Name = registrationAddress.City },
                        District = new DistrictDTO(){ Name = registrationAddress.District }
                    }
                ];
            }

            if (request.KIDs is not null && request.KIDs.Any())
            {
                foreach (var kid in request.KIDs)
                {
                    var kidDTO = await kidsService.GetKidAsync(kid.Sector, kid.Section, kid.Code, kid.KIDVersion.ValidFrom);

                    if (kidDTO is null)
                        continue;

                    companyDTO.Kids.Add(kidDTO);
                }
            }

            return companyDTO;
        }

        public TypeOfAppointment GetContractType(NomenclatureDTO contract, NomenclatureDTO incomeType)
        {
            switch (contract.Identifier)
            {
                case "2":
                    if (incomeType is null
                        || string.IsNullOrEmpty(incomeType.Identifier)
                        || _civilContractStandartCodes.Contains(incomeType.Identifier))
                    {
                        return TypeOfAppointment.CivilContractStandart;
                    }
                    else
                    {
                        if (_civilContractLandlordsCodes.Contains(incomeType.Identifier))
                        {
                            return TypeOfAppointment.CivilContractLandlords;
                        }
                        else if (_civilContractDividendsCodes.Contains(incomeType.Identifier))
                        {
                            return TypeOfAppointment.CivilContractDividends;
                        }
                        else
                        {
                            return TypeOfAppointment.CivilContractOther;
                        }
                    }
                case "3":
                    return TypeOfAppointment.SelfEmployedOwner;
                case "4":
                    return TypeOfAppointment.ManagementContracts;
                case "6":
                    return TypeOfAppointment.EmploymentRelationship;
                default:
                    return TypeOfAppointment.EmploymentContracts;
            }
        }
    }
}
