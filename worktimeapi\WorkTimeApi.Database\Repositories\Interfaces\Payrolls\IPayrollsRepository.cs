using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.TRZ;

namespace WorkTimeApi.Database.Repositories.Interfaces.Payrolls
{
	public interface IPayrollsRepository : IBaseRepository<Payroll>
	{
		Task<Payroll> AddPayrollAsync(Payroll payroll);

		Task<IEnumerable<Payroll>> GetPayrollsAsync(Guid companyId);

        Task<Payroll?> GetPayrollAsync(Guid? id);

        Task<List<Payroll>?> GetPayrollsByEmployeeIdAsync(Guid Id);

        Task DeletePayrollAsync(string payrollId);

        Task<List<TRZPayroll>> GetPendingPayrollsAsync(Guid companyId);

        Task<Payroll?> GetPayrollWithAnnexPayrollsAsync(Guid payrollId);

        Task UpdateAdditionalTermsAsync(Guid payrollId, string additionalTerms);

    }
}
