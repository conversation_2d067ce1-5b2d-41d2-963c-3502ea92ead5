﻿using Microsoft.EntityFrameworkCore;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;

namespace WorkTimeApi.Database.Repositories.Companies
{
    public class CompaniesRepository(IDbContextFactory<WorkTimeApiDbContext> dbContextFactory)
        : BaseRepository<Company>(dbContextFactory), ICompaniesRepository
    {
        public async Task<IEnumerable<Company>> GetActiveCompaniesAsync(Guid userId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees
                .Include(e => e.Company)
                .Where(e => e.UserId == userId && e.Status == EmployeeStatus.Active)
                .Select(ecs => ecs.Company!)
                .Distinct()
                .ToListAsync();
        }

        public async Task<IEnumerable<Company>> GetPendingCompaniesAsync(Guid userId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Employees
                .Include(e => e.Company)
                .Where(e => e.UserId == userId && e.Status == EmployeeStatus.UserToApprove)
                .Select(ecs => ecs.Company!)
                .Distinct()
                .ToListAsync();
        }

        public async Task<Company?> GetCompanyAsync(Guid id)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Companies.FindAsync(id);
        }

        public async Task<Company?> GetCompanyByUserRegistrarionsIdAsync(int userRegistrarionsId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Companies.FirstOrDefaultAsync(c => c.UserRegistrationCompanyId == userRegistrarionsId);
        }

        public async Task<Company> CreateCompanyAsync(Company company, Guid userId, EmployeeStatus status)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingCompany = await context.Companies.FirstOrDefaultAsync(c => c.UserRegistrationCompanyId == company.UserRegistrationCompanyId);
            if (existingCompany is not null)
                return existingCompany;

            var existingEmployee = await context.Employees.FirstOrDefaultAsync(e => e.UserId == userId && e.CompanyId == company.Id);
            if (existingEmployee == null)
            {
                var user = await context.Users.FirstOrDefaultAsync(u => u.Id == userId);
                var newEmployee = new Employee
                {
                    UserId = userId,
                    FirstName = user.FirstName,
                    SecondName = user.SecondName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Company = company,
                    Status = status
                };
                newEmployee.EmployeeRoles = new List<RoleEmployee>
                {
                    new() { EmployeeId = newEmployee.Id, RoleId = new Guid(DefaultWorktimeRole.Creator) },
                    new() { EmployeeId = newEmployee.Id, RoleId = new Guid(DefaultWorktimeRole.Owner) }
                };

                await context.Employees.AddAsync(newEmployee);
                await context.SaveChangesAsync();
                company.Employees.Add(newEmployee);
                return company;
            }

            existingEmployee.Company = company;
            existingEmployee.Status = status;

            existingEmployee.EmployeeRoles.Add(new RoleEmployee()
            {
                EmployeeId = existingEmployee.Id,
                RoleId = new Guid(DefaultWorktimeRole.Owner)
            });
            existingEmployee.EmployeeRoles.Add(new RoleEmployee()
            {
                EmployeeId = existingEmployee.Id,
                RoleId = new Guid(DefaultWorktimeRole.Creator)
            });

            await context.SaveChangesAsync();
            company.Employees.Add(existingEmployee);

            return company;
        }

        public async Task AddCompanyAddressesAsync(IEnumerable<CompanyAddress> companyAddresses)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            await context.CompanyAddresses.AddRangeAsync(companyAddresses);

            await context.SaveChangesAsync();
        }

        public async Task AddCompanyKidsAsync(IEnumerable<CompanyKid> companyKids)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var companyKid = companyKids.FirstOrDefault();
            if (companyKid is null)
                return;

            var existingKids = await context.CompanyKids.Where(ck => ck.CompanyId == companyKid.CompanyId && ck.KidId == companyKid.KidId).ToListAsync();
            if (existingKids is not null && existingKids.Count != 0)
                return;

            await context.CompanyKids.AddRangeAsync(companyKids);

            await context.SaveChangesAsync();
        }

        public async Task<bool> IsEmployeeCompanyExistingAsync(Employee employee)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingEmployeeCompany = await context
                .Employees
                .FirstOrDefaultAsync(ec => ec.CompanyId == employee.CompanyId
                    && ec.Id == employee.Id
                    && ec.Status == EmployeeStatus.Active);

            return existingEmployeeCompany is not null;
        }

        public async Task<bool> CheckIfCompanyAlreadyExistsAsync(Company company)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingCompany = await context.Companies.FirstOrDefaultAsync(c => c.UserRegistrationCompanyId == company.UserRegistrationCompanyId);

            return existingCompany is not null;
        }

        public async Task<Company> UpdateCompanyAsync(Company company)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingCompany = await context.Companies.FindAsync(company.Id);
            if (existingCompany is null)
                return company;

            context.Entry(existingCompany).CurrentValues.SetValues(company);
            await context.SaveChangesAsync();

            return existingCompany;
        }

        public async Task<Employee> ShareCompanyAsync(User user, Employee employee, Guid companyId, Guid roleId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var company = await context.Companies.FirstOrDefaultAsync(c => c.Id == companyId);
            if (company is null)
                return null;

            var existingUser = await context.Users.FirstOrDefaultAsync(u => u.Email.ToLower() == user.Email.ToLower());
            if (existingUser is null)
            {
                await context.Users.AddAsync(user);
                await context.SaveChangesAsync();
                existingUser = user;
            }

            var existingEmployee = await context.Employees
                .FirstOrDefaultAsync(ec => ec.UserId == existingUser.Id && ec.CompanyId == company.Id);

            if (existingEmployee is not null)
            {
                if (existingEmployee.Status != EmployeeStatus.Declined)
                {
                    existingEmployee.EmployeeRoles.Clear();
                    existingEmployee.EmployeeRoles.Add(new RoleEmployee { EmployeeId = employee.Id, RoleId = roleId });
                    await context.SaveChangesAsync();
                    return existingEmployee;
                }

                existingEmployee.Status = EmployeeStatus.UserToApprove;
                await context.SaveChangesAsync();
                return existingEmployee;
            }

            employee.UserId = existingUser.Id;
            employee.EmployeeRoles.Add(new RoleEmployee { EmployeeId = employee.Id, RoleId = roleId });
            employee.Status = EmployeeStatus.UserToApprove;

            context.Employees.Add(employee);
            await context.SaveChangesAsync();

            return employee;
        }

        public async Task<Company?> GetCompanyAsync(string email, string bulstat)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return (await context.Employees
                .Include(c => c.Company)
                .FirstOrDefaultAsync(e => e.Email == email && e.Company.Bulstat == bulstat))
                ?.Company;
        }

        public async Task RemoveCompanyAddressesAsync(Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var addresses = context.CompanyAddresses.Where(c => c.CompanyId == companyId).Include(c => c.Address).Select(c => c.Address);

            context.CompanyAddresses.RemoveRange(context.CompanyAddresses.Where(ca => ca.CompanyId == companyId));
            context.Addresses.RemoveRange(addresses);

            await context.SaveChangesAsync();
        }

        public async Task RemoveCompanyKidsAsync(Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            context.CompanyKids.RemoveRange(context.CompanyKids.Where(ca => ca.CompanyId == companyId));

            await context.SaveChangesAsync();
        }

        public async Task DeleteCompanyAsync(Guid companyId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();
            var company = await context.Companies.FirstOrDefaultAsync(c => c.Id == companyId);

            if (company == null)
                return;

            context.Employees.RemoveRange(context.Employees.Where(e => e.CompanyId == companyId));
            context.CompanyKids.RemoveRange(context.CompanyKids.Where(ca => ca.CompanyId == companyId));
            context.Companies.Remove(company);

            await context.SaveChangesAsync();
        }

    }
}
