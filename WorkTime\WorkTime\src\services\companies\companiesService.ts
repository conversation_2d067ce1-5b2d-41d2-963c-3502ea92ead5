import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";
import { authenticatedGet, authenticatedPost } from "../connectionService";
import { CompanyRequestRequest } from "../../models/Requests/Companies/CompanyRequestRequest";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import {
  authenticatedDelete as authenticatedDeleteWorktime,
  authenticatedPost as authenticatedPostWorktime,
} from "../worktimeConnectionService";

export const initCompany = async (): Promise<CompanyDTO> => {
  const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

  var companyDb = await getCompanyById(companyId);
  if (companyId && companyDb)
    return {
      id: companyId,
      name: companyDb.name ?? "",
      bulstat: companyDb.bulstat ?? "",
      userRegistrationCompanyId: companyDb.userRegistrationCompanyId ?? 0,
      contactName: companyDb.contactName ?? "",
    };

  return {
    id: "",
    name: "",
    bulstat: "",
    userRegistrationCompanyId: 0,
    contactName: "",
  };
};

export const getRequestedCompany = async (email: string, bulstat: string) => {
  return await authenticatedGet(`company/${email}/${bulstat}`);
};

export const joinCompanyRequest = async (
  companyRequest: CompanyRequestRequest | undefined
) => {
  return await authenticatedPost(`join-company-request`, companyRequest);
};

export const getCompanyById = async (id: string) => {
  if (!id || id === "") return undefined;

  return await authenticatedGet<CompanyDTO>(`company?companyId=${id}`);
};

export const setCompanyLocalStorage = (company: CompanyDTO) => {
  localStorage.setItem(LOCAL_STORAGE_COMPANY_ID, company.id);
};

export const shareCompany = async (
  email: string,
  companyId: string,
  roleId: string,
  roleName: string
) => {
  return await authenticatedPost(`company/share`, {
    email,
    companyId,
    roleId,
    roleName,
  });
};

export const leaveCompany = async (companyId: string) => {
  return await authenticatedPostWorktime(`leave-company`, {
    companyId,
  });
};

export const deleteCompany = async (companyId: string) => {
  return await authenticatedDeleteWorktime(`delete-company/`, companyId);
};

export const changeUserRole = async (
  userId: string,
  companyId: string,
  oldRoleId: string | undefined,
  newRoleId: string,
  isNewlyAddedEmployee: boolean
) => {
  return await authenticatedPost(`company/user/roles`, {
    userId,
    companyId,
    oldRoleId,
    newRoleId,
    isNewlyAddedEmployee,
  });
};
