﻿using Gateway.Common.Requests;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Common.Requests.Employees
{
	public class AddEmployeeRequest : BaseRequest
	{
		public Guid Id { get; set; }

		public string? FirstName { get; set; }

		public string? SecondName { get; set; }

		public string? LastName { get; set; }

		public string? EGN { get; set; }

		public string? Email { get; set; }

		public string? Phone { get; set; }

        public bool HasSignedIn { get; set; } = true;

        public Guid? CompanyId { get; set; }

        public EmployeeStatus? Status { get; set; }
    }
}
