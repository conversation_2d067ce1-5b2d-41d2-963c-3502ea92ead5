﻿using AutoMapper;
using Gateway.Common.Results;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.DTOs.TRZ;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.PedningEmployees;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.TRZ;
using WorkTimeApi.Database.Repositories.Interfaces.Addresses;
using WorkTimeApi.Database.Repositories.Interfaces.StructureLevels;
using WorkTimeApi.Database.Repositories.Interfaces.TRZ;
using WorkTimeApi.Mappers.Interfaces;
using WorkTimeApi.Models.Mediator;
using WorkTimeApi.Services.Interfaces.TRZ;
using WorkTimeApi.Services.StructureLevels;
using WorkTimeApi.Validators.Interfaces.TRZ;

namespace WorkTimeApi.Services.TRZ
{
    public class TRZService : ITRZService
    {
        private readonly IMapper _mapper;
        private readonly IEmployeesListMapper _employeesListMapper;
        private readonly ITRZRepository _trzEmployeesRepository;
        private readonly IStructureLevelsRepository _structureLevelsRepository;
        private readonly ITRZEmployeeValidator _trzEmployeeValidator;
        private readonly ITRZAbsenceValiadtor _trzAbsenceValidator;
        private readonly IAddressesRepository _addressesRepository;
        private readonly IMediator _mediator;

        public TRZService(ITRZRepository trzEmployeesRepository,
            ITRZEmployeeValidator trzEmployeeValidator,
            ITRZAbsenceValiadtor trzAbsenceValidator,
            IMapper mapper,
            IEmployeesListMapper employeesListMapper,
            IStructureLevelsRepository structureLevelsRepository,
            IAddressesRepository addressesRepository,
            IMediator mediator)
        {
            _trzEmployeesRepository = trzEmployeesRepository;
            _trzEmployeeValidator = trzEmployeeValidator;
            _trzAbsenceValidator = trzAbsenceValidator;
            _mapper = mapper;
            _employeesListMapper = employeesListMapper;
            _structureLevelsRepository = structureLevelsRepository;
            _addressesRepository = addressesRepository;
            _mediator = mediator;
        }

        public async Task<IEnumerable<LightPayrollDTO>> ImportTRZEmployeesAsync(List<Guid> PendingPayrollIds, Guid companyId)
        {
            var allPayrolls = new List<Payroll>();
            var employeesToImport = await _trzEmployeesRepository.GetPendingEmployeesAsync(PendingPayrollIds);

            foreach (var employee in employeesToImport)
            {
                employee.PendingPayrolls = employee.PendingPayrolls
                    .Where(pp => PendingPayrollIds.Contains(pp.Id))
                    .ToList();

                foreach (var pendingPayroll in employee.PendingPayrolls)
                {
                    var payroll = _mapper.Map<Payroll>(pendingPayroll);
                    foreach (var pendingEvent in pendingPayroll.Events)
                    {
                        if (pendingEvent.IsHospital)
                            payroll.Hospitals.Add(_mapper.Map<Hospital>(pendingEvent));
                        else
                            payroll.Absences.Add(_mapper.Map<Absence>(pendingEvent));
                    }

                    allPayrolls.Add(payroll);
                }
            }

            var employeesResult = await _trzEmployeesRepository.AddPenidngEmployeePayrollsAsync(employeesToImport.ToList(), companyId, allPayrolls);
            return _mapper.Map<IEnumerable<LightPayrollDTO>>(employeesResult.SelectMany(e => e.Payrolls));
        }

        public async Task<Result<List<EmployeeDTO>>> AddTRZEmployeesAsync(List<EmployeeDTO> employeesDTO, Guid companyId)
        {
            var errors = new ValidationResult();

            foreach (var employee in employeesDTO)
            {
                var validationResult = await _trzEmployeeValidator.ValidateTRZPendingEmployeeAsync(employee);
                if (validationResult.HasValidationErrors)
                    errors.ValidationErrors.AddRange(validationResult.ValidationErrors);
            }

            if (errors.HasValidationErrors)
                return errors;

            var employees = new List<Employee>();
            foreach (var employeeDTO in employeesDTO)
            {
                var employee = _mapper.Map<Employee>(employeeDTO);
                var user = _mapper.Map<User>(employeeDTO.User);
                employee.User = user;

                employee.Status = EmployeeStatus.PendingTRZ;
                employee.EmployeeRoles.Add(new RoleEmployee { EmployeeId = employee.Id, RoleId = new Guid(DefaultWorktimeRole.Employee) });
                employee.LengthsOfService = _mapper.Map<List<LengthOfService>>(employeeDTO.LengthsOfService);

                if (employeeDTO.Addresses is not null && employeeDTO.Addresses.Any())
                {
                    foreach (var addressDTO in employeeDTO.Addresses)
                    {
                        var address = _mapper.Map<Address>(addressDTO);
                        if (address is null)
                            continue;

                        var city = await _addressesRepository.GetCityByNameAndPostalCodeAsync(addressDTO?.City?.Name, address.PostalCode);
                        address.CityId = city?.Id;
                        address.City = null;

                        var district = await _addressesRepository.GetDistrictByNameAsync(addressDTO?.District?.Name);
                        address.DistrictId = district?.Id;
                        address.District = null;

                        var municipality = await _addressesRepository.GetMunicipalityByNameAsync(addressDTO?.Municipality?.Name);
                        address.MunicipalityId = municipality?.Id;
                        address.Municipality = null;

                        if (address.CityId != null || address.MunicipalityId != null || address.DistrictId != null
                            || !string.IsNullOrEmpty(address.CityName) || !string.IsNullOrEmpty(address.MunicipalityName) || !string.IsNullOrEmpty(address.DistrictName))
                            employee.EmployeeAddresses.Add(new EmployeeAddress { Employee = employee, Address = address });
                    }
                }

                if (employeeDTO.BankAccounts is not null && employeeDTO.BankAccounts.Any())
                {
                    foreach (var bankAccountDTO in employeeDTO.BankAccounts)
                    {
                        var bankAccount = _mapper.Map<BankAccount>(bankAccountDTO);
                        employee.EmployeeBankAccounts.Add(new EmployeeBankAccount { BankAccount = bankAccount, EmployeeId = employee.Id });
                    }
                }

                employees.Add(employee);
            }

            var employeesResult = await _trzEmployeesRepository.AddTRZEmployeesAsync(employees, companyId);

            foreach (var employee in employeesResult)
                await _mediator.Publish(new AddDefaultNotificationSettings(employee.Id, new Guid(DefaultWorktimeRole.Employee)));

            var payrollDTOs = _employeesListMapper.MapPendingEmployeesWithPayrollDetails(employeesResult);

            var notification = new PendingEmployeesNotification(payrollDTOs, companyId);

            await _mediator.Publish(notification);
            return _mapper.Map<List<EmployeeDTO>>(employeesResult);
        }

        public async Task<Result<List<EventDTO>>> AddTRZPendingEventsAsync(List<EventDTO> pendingEventsDTO)
        {
            var errors = new ValidationResult();

            foreach (var eventDTO in pendingEventsDTO)
            {
                var validationResult = await _trzAbsenceValidator.ValidateTRZPendingAbsencesAsync(eventDTO);
                if (validationResult.HasValidationErrors)
                    errors.ValidationErrors.AddRange(validationResult.ValidationErrors);
            }

            if (errors.HasValidationErrors)
                return errors;

            var events = await _trzEmployeesRepository.AddTRZPendingEventsAsync(_mapper.Map<List<TRZEvent>>(pendingEventsDTO));

            return _mapper.Map<List<EventDTO>>(events);
        }

        public async Task<IEnumerable<EventDTO>> GetTRZEventsAsync(Guid payrollId)
        {
            var trzEvents = _mapper.Map<IEnumerable<EventDTO>>(await _trzEmployeesRepository.GetTRZEventsAsync(payrollId));

            return trzEvents;
        }

        public async Task<Result<AbsenceDTO>> ImportTRZAbsenceAsync(AbsenceDTO absenceDTO)
        {
            var absence = await _trzEmployeesRepository.ImportAbsenceAsync(_mapper.Map<Absence>(absenceDTO));

            return _mapper.Map<AbsenceDTO>(absence);
        }

        public async Task<Result<HospitalDTO>> ImportTRZHospitalAsync(HospitalDTO hospitalDTO)
        {
            var hospital = await _trzEmployeesRepository.ImportHospitalAsync(_mapper.Map<Hospital>(hospitalDTO));

            return _mapper.Map<HospitalDTO>(hospital);
        }

        public async Task DeletePendingEventAsync(Guid pendingEventId)
        {
            await _trzEmployeesRepository.DeletePendingEventAsync(pendingEventId);
        }

        public async Task<IEnumerable<EmployeeDTO>> GetTRZEmployeesAsync(Guid companyId)
        {
            var trzEmployees = _mapper.Map<IEnumerable<EmployeeDTO>>(await _trzEmployeesRepository.GetTRZEmployeesAsync(companyId));

            return trzEmployees;
        }

        public async Task<TRZDepartmentDTO> UpdateTRZDepartmentsAsync(TRZDepartmentDTO rootDepartment, Guid companyId)
        {
            var rootStructureLevel = _mapper.Map<StructureLevel>(rootDepartment);
            var newStructureLevels = new List<StructureLevel>();
            var updatedStructureLevels = new List<StructureLevel>();
            var currentStructureLevels = await _structureLevelsRepository.GetStructureLevelsByCompanyIdAsync(companyId);

            StructureLevelsService.TraversStructureLevelsTree(rootStructureLevel, (s) =>
            {
                if (s.Id == Guid.Empty)
                    newStructureLevels.Add(s);
                else if (currentStructureLevels.Any(sl => sl.Id == s.Id))
                    updatedStructureLevels.Add(s);

                s.CompanyId = companyId;
                s.Type = StructureLevelType.Department;
            });

            var deletedStructureLevels = currentStructureLevels.ExceptBy(updatedStructureLevels.Select(sl => sl.Id), sl => sl.Id);

            if (newStructureLevels != null && newStructureLevels.Count > 0)
                await _structureLevelsRepository.AddStructureLevelsAsync(newStructureLevels);

            if (updatedStructureLevels != null && updatedStructureLevels.Count > 0)
                await _structureLevelsRepository.UpdateStructureLevelsAsync(updatedStructureLevels);

            if (deletedStructureLevels != null && deletedStructureLevels.Any())
            {
                await _structureLevelsRepository.MovePayrollsFromDeletedStructureLevelsAsync(deletedStructureLevels);
                await _structureLevelsRepository.DeleteStructureLevelsAsync(deletedStructureLevels);
            }

            return _mapper.Map<TRZDepartmentDTO>(rootStructureLevel);
        }

        public async Task DeletePendingPayrollsAsync(IEnumerable<Guid> pendingEmployeeIds)
        {
            foreach (var pendingEmployeeId in pendingEmployeeIds)
            {
                await _trzEmployeesRepository.DeletePendingPayrollsAsync(pendingEmployeeId);
            }
        }
    }
}
