using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Extensions;
using WorkTimeApi.Database.Models.Notifications;
using WorkTimeApi.Database.Models.TRZ;

namespace WorkTimeApi.Database.Models
{
    public class Employee : BaseObjectIndexer
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public string? FirstName { get; set; }

        public string? SecondName { get; set; }

        public string? LastName { get; set; }

        public string? EGN { get; set; }

        public EGNTypes EGNType { get; set; }

        public string? Email { get; set; }

        public string? WorkEmail { get; set; }

        public string? Phone { get; set; }

        public string? WorkPhone { get; set; }

        public string? IDNumber { get; set; }

        public string? IDIssuedFrom { get; set; }

        public DateTime? IDIssueDate { get; set; }

        public DateTime? BirthDate { get; set; }

        public string? BirthPlace { get; set; }

        public bool IsForeigner { get; set; }

        public Genders Gender { get; set; }

        public string? Citizenship { get; set; }

        public string? Number { get; set; }

        public EducationType Education { get; set; }

        public DateTime UploadDate { get; set; } = DateTime.Now;

        public Guid CompanyId { get; set; }

        public Company Company { get; set; }

        public EmployeeStatus Status { get; set; } = EmployeeStatus.Active;

        public Guid UserId { get; set; }

        public User User { get; set; }

        public ICollection<TRZPayroll> PendingPayrolls { get; set; } = [];

        public ICollection<Payroll> Payrolls { get; set; } = [];

        public ICollection<EmployeeAddress> EmployeeAddresses { get; set; } = [];

        public ICollection<LengthOfService> LengthsOfService { get; set; } = [];

        public ICollection<RoleEmployee> EmployeeRoles { get; set; } = [];

        public ICollection<NotificationSetting> NotificationSettings { get; set; } = [];

        public ICollection<Notification> Notifications { get; set; } = [];

        public ICollection<EmployeeBankAccount> EmployeeBankAccounts { get; set; } = [];

        public ICollection<EmployeePropertyEdit> EmployeePropertyEdits { get; set; }

        [NotMapped]
        public int TRZId { get; set; }
    }
}
