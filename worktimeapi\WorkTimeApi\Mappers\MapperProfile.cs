using AutoMapper;
using SSO.Common.DTOs;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.DTOs.Addresses;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.DTOs.Notifications;
using WorkTimeApi.Common.DTOs.OnboardingDocuments;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.DTOs.StructureLevels;
using WorkTimeApi.Common.DTOs.Templates;
using WorkTimeApi.Common.DTOs.TRZ;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.ResponseObjects;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.Notifications;
using WorkTimeApi.Database.Models.TRZ;
using WorkTimeApi.Mappers.ValueResolver;

namespace WorkTimeApi.Mappers
{
    public class MapperProfile : Profile
    {
        public MapperProfile()
        {
            CreateMap<TRZPayrollDTO, PayrollDTO>()
               .ForMember(dest => dest.Kids, opt => opt.Ignore())
               .ForMember(dest => dest.Kpds, opt => opt.Ignore())
               .ReverseMap();

            CreateMap<TRZPayrollDTO, AnnexPayrollDTO>()
             .ForMember(dest => dest.Kids, opt => opt.Ignore())
             .ForMember(dest => dest.Kpds, opt => opt.Ignore())
             .ReverseMap();

            CreateMap<AnnexPayrollDTO, TRZPayrollDTO>()
                .ForMember(dest => dest.MainPayrollWorktimeGuid, opt => opt.MapFrom(a => a.MainPayrollWorktimeGuid)).ReverseMap()
                .ForMember(dest => dest.MainPayrollId, opt => opt.MapFrom(a => a.TRZPayrollId))
                .ReverseMap();

            CreateMap<PayrollDTO, TRZPayroll>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(a => a.WorkTimeId))
               .ForMember(dest => dest.TRZId, opt => opt.MapFrom(a => a.TRZPayrollId))
               .ForMember(dest => dest.EmployeeId, opt => opt.MapFrom(a => a.EmployeeGuid))
               .ForMember(dest => dest.Events, opt => opt.MapFrom(a => a.PendingEvents))
               .ForMember(d => d.IncomeType, opt => opt.MapFrom((s) =>
                    s.IncomeType != null && s.IncomeType.Identifier != null
                        ? (IncomeType)Convert.ToInt32(s.IncomeType.Identifier)
                        : (IncomeType?)null))
               .ForMember(dest => dest.Position, opt => opt.MapFrom(src => src.Position.Name))
               .ForMember(d => d.KIDs, opt => opt.MapFrom<PayrollKidsResolver>())
               .ForMember(d => d.KPDs, opt => opt.MapFrom<PayrollKpdsResolver>());

            CreateMap<NomenclatureDTO?, TypeOfAppointment>()
                .ConvertUsing(src => src.ToTypeOfAppointment());

            CreateMap<TypeOfAppointment, NomenclatureDTO?>()
                .ConvertUsing(src => src.ToNomenclatureDTO());

            CreateMap<NomenclatureDTO?, ContractReason>()
                .ConvertUsing(src => src.ToContractReason());

            CreateMap<ContractReason, NomenclatureDTO?>()
                .ConvertUsing(src => src.ToNomenclatureDTO());

            CreateMap<NomenclatureDTO?, EGNTypes>()
                .ConvertUsing(src => src.ToENGType());

            CreateMap<EGNTypes, NomenclatureDTO?>()
                .ConvertUsing(src => src.ToNomenclatureDTO());

            CreateMap<TRZAddressPurposeDTO?, AddressPurpose>()
                .ConvertUsing(src => src.ToAddressPurpose());

            CreateMap<AddressPurpose, TRZAddressPurposeDTO?>()
                .ConvertUsing(src => src.ToTRZAddressPurposeDTO());

            CreateMap<NomenclatureDTO?, BankAccountPurpose>()
                .ConvertUsing(src => src.ToBankAccountPurpose());

            CreateMap<EnumValueDTO?, BankAccountPurpose>()
                .ConvertUsing(src => src.ToBankAccountPurpose());

            CreateMap<BankAccountPurpose, EnumValueDTO?>()
               .ConvertUsing(src => src.ToEnumValueDTO());

            CreateMap<AddressPurpose, NomenclatureDTO?>()
                .ConvertUsing(src => src.ToNomenclatureDTO());

            CreateMap<Country, EnumValueDTO?>()
                .ConvertUsing(src => src.ToEnumValueDTO());

            CreateMap<EnumValueDTO?, Country>()
                .ConvertUsing(src => src.ToCountry());

            CreateMap<TRZPayroll, PayrollDTO>()
                .ForMember(dest => dest.WorkTimeId, opt => opt.MapFrom(a => a.Id))
                .ForMember(dest => dest.Kpds, opt => opt.Ignore())
                .ForMember(dest => dest.Kids, opt => opt.Ignore())
                .ForMember(dest => dest.TRZPayrollId, opt => opt.MapFrom(a => a.TRZId))
                .ForMember(dest => dest.EmployeeGuid, opt => opt.MapFrom(a => a.EmployeeId))
                .ForMember(dest => dest.PendingEvents, opt => opt.MapFrom(a => a.Events))
                .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src =>
                    src.IncomeType.HasValue
                    ? new NomenclatureDTO { Identifier = src.IncomeType.Value.ToString() }
                    : null))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src =>
                     new NomenclatureDTO
                     {
                         Identifier = "0",
                         Name = src.Position ?? string.Empty,
                         Description = string.Empty
                     }));

            CreateMap<AnnexPayrollDTO, TRZAnnexPayroll>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(a => a.WorkTimeId))
               .ForMember(dest => dest.TRZId, opt => opt.MapFrom(a => a.TRZPayrollId))
               .ForMember(dest => dest.TRZPayrollId, opt => opt.MapFrom(a => a.MainPayrollWorktimeGuid))
               .ForMember(dest => dest.ContractReason, opt => opt.MapFrom(src => src.ContractReason))
               .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src => src.IncomeType != null
                    && Enum.IsDefined(typeof(IncomeType), src.IncomeType.Identifier)
                    ? src.IncomeType.Identifier.ToString()
                    : null))
                       .ForMember(dest => dest.Position, opt =>
                        opt.MapFrom(src => src.Position.Name))
               .ForMember(d => d.KIDs, opt => opt.MapFrom<AnnexPayrollKidsResolver>())
               .ForMember(d => d.KPDs, opt => opt.MapFrom<AnnexPayrollKpdsResolver>());

            CreateMap<TRZAnnexPayroll, AnnexPayrollDTO>()
                .ForMember(dest => dest.WorkTimeId, opt => opt.MapFrom(a => a.Id))
                .ForMember(dest => dest.TRZPayrollId, opt => opt.MapFrom(a => a.TRZId))
                .ForMember(dest => dest.MainPayrollWorktimeGuid, opt => opt.MapFrom(a => a.TRZPayrollId))
                .ForMember(dest => dest.ContractReason, opt => opt.MapFrom(src =>
                    src.ContractReason.HasValue
                    ? new NomenclatureDTO { Identifier = src.ContractReason.Value.ToString() }
                    : null))
                .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src =>
                    src.IncomeType.HasValue
                    ? new NomenclatureDTO { Identifier = src.IncomeType.Value.ToString() }
                    : null))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src =>
                       new NomenclatureDTO
                       {
                           Identifier = "0",
                           Name = src.Position ?? string.Empty,
                           Description = string.Empty
                       }));

            CreateMap<Payroll, PayrollDTO>()
                .ForMember(dest => dest.WorkTimeId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.EmployeeGuid, opt => opt.MapFrom(src => src.EmployeeId))
                .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src =>
                    src.IncomeType.HasValue
                        ? new NomenclatureDTO { Identifier = src.IncomeType.Value.ToString() }
                        : null))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src =>
                    new NomenclatureDTO { Name = src.Position }));

            CreateMap<PayrollDTO, Payroll>()
               .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src => src.IncomeType != null
                    && Enum.IsDefined(typeof(IncomeType), src.IncomeType.Identifier)
                    ? src.IncomeType.Identifier.ToString()
                    : null));

            CreateMap<AnnexPayrollDTO, AnnexPayroll>()
               .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src =>
                    src.IncomeType != null
                    && Enum.IsDefined(typeof(IncomeType), src.IncomeType.Identifier)
                    ? src.IncomeType.Identifier.ToString()
                    : null));

            CreateMap<AnnexPayroll, AnnexPayrollDTO>()
                .ForMember(dest => dest.MainPayrollId, opt => opt.Ignore())
                .ForMember(dest => dest.ContractReason, opt => opt.MapFrom(src =>
                    src.ContractReason.HasValue
                    ? new NomenclatureDTO { Identifier = src.ContractReason.Value.ToString() }
                    : null))
                .ForMember(dest => dest.IncomeType, opt => opt.MapFrom(src =>
                    src.IncomeType.HasValue
                    ? new NomenclatureDTO { Identifier = src.IncomeType.Value.ToString() }
                    : null))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src =>
                     new NomenclatureDTO { Name = src.Position }));

            CreateMap<EmployeeDTO, Employee>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(a => a.WorkTimeId))
                .ForMember(dest => dest.TRZId, opt => opt.MapFrom(a => a.TRZEmployeeId))
                .ReverseMap()
                .ForMember(dest => dest.Addresses, opt => opt.MapFrom(src => src.EmployeeAddresses.Select(ea => ea.Address)))
                .ForMember(dest => dest.BankAccounts, opt => opt.MapFrom(src => src.EmployeeBankAccounts.Select(ea => ea.BankAccount)));

            CreateMap<TRZEmployeeDTO, EmployeeDTO>()
                .ForMember(dest => dest.Gender, opt => opt.MapFrom(a => a.Gender == true ? Genders.Female : Genders.Male))
                .ReverseMap();

            CreateMap<TRZAddressDTO, AddressDTO>()
              .ReverseMap();

            CreateMap<BankAccountDTO, BankAccount>()
              .ReverseMap();

            CreateMap<TRZPayrollKid, PayrollKID>()
              .ReverseMap();

            CreateMap<TRZPayrollKPD, PayrollKPD>()
              .ReverseMap();

            CreateMap<TRZAnnexPayrollKid, AnnexPayrollKID>()
              .ReverseMap();

            CreateMap<TRZAnnexPayrollKPD, AnnexPayrollKPD>()
              .ReverseMap();

            CreateMap<Common.DTOs.Users.UserDTO, User>().ReverseMap();
            CreateMap<UserDTO, User>().ReverseMap();

            CreateMap<TRZPayroll, Payroll>()
                .ForMember(dest => dest.Employee, opt => opt.Ignore())
                .ForMember(dest => dest.PayrollKIDs, opt => opt.MapFrom(src => src.KIDs))
                .ForMember(dest => dest.PayrollKPDs, opt => opt.MapFrom(src => src.KPDs))
                .ReverseMap();

            CreateMap<TRZAnnexPayroll, AnnexPayroll>()
                .ForMember(dest => dest.AnnexPayrollKIDs, opt => opt.MapFrom(src => src.KIDs))
                .ForMember(dest => dest.AnnexPayrollKPDs, opt => opt.MapFrom(src => src.KPDs))
               .ReverseMap();

            CreateMap<TRZLengthOfServiceDTO, LengthOfServiceDTO>()
               .ForMember(dest => dest.Years, opt => opt.MapFrom(src => src.LOSYears))
               .ForMember(dest => dest.Months, opt => opt.MapFrom(src => src.LOSMonths))
               .ForMember(dest => dest.Days, opt => opt.MapFrom(src => src.LOSDays))
               .ForMember(dest => dest.Month, opt => opt.MapFrom(src => src.Month))
               .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type));

            CreateMap<LengthOfService, LengthOfServiceDTO>().ReverseMap();

            CreateMap<TemplateDTO, Template>().ReverseMap();

            CreateMap<OnboardingDocumentDTO, OnboardingDocument>().ReverseMap();

            CreateMap<StructureLevelDTO, StructureLevel>().ReverseMap();

            CreateMap<StructureLevelResponseObject, StructureLevel>().ReverseMap();

            CreateMap<CompanyDTO, Company>().ReverseMap();

            CreateMap<AddEmployeeRequest, EmployeeDTO>()
                .ForMember(dest => dest.WorkTimeId, opt => opt.MapFrom(a => a.Id)).ReverseMap();

            CreateMap<Address, AddressDTO>().ReverseMap();

            CreateMap<Payroll, PayrollPersonalDataDTO>().ReverseMap();

            CreateMap<EmployeeAddress, AddressDTO>().ReverseMap();

            CreateMap<TRZAddressDTO, AddressDTO>()
                .ForMember(dest => dest.City, opt => opt.Ignore())
                .ForMember(dest => dest.Municipality, opt => opt.Ignore())
                .ForMember(dest => dest.District, opt => opt.Ignore())
                .ForMember(dest => dest.Country, opt => opt.Ignore()).ReverseMap();

            CreateMap<QualificationGroup, QualificationGroupDTO>();

            CreateMap<District, DistrictDTO>().ReverseMap();

            CreateMap<Municipality, MunicipalityDTO>().ReverseMap();

            CreateMap<City, CityDTO>().ReverseMap();

            CreateMap<TZPB, TZPBDTO>();

            CreateMap<Mod, ModDTO>();

            CreateMap<KPD, NKPDDTO>();

            CreateMap<KPD, KpdDTO>();

            CreateMap<TRZKpdDTO, KpdDTO>();

            CreateMap<TRZPayrollKPD, NKPDDTO>();

            CreateMap<TRZAnnexPayrollKPD, NKPDDTO>();

            CreateMap<TRZAnnexPayrollKPD, KpdDTO>();

            CreateMap<Kid, KidDTO>().ReverseMap();

            CreateMap<Kid, TRZKidDTO>().ReverseMap();

            CreateMap<KidDTO, TRZKidDTO>().ReverseMap();

            CreateMap<TRZPayrollKid, KidDTO>().ReverseMap();

            CreateMap<TRZAnnexPayrollKid, KidDTO>().ReverseMap();

            CreateMap<TRZEventDTO, TRZEvent>()
                .ForMember(dest => dest.IsHospital, opt => opt.MapFrom(src => src.EventType.IsHospital()))
                .ReverseMap();

            CreateMap<EventDTO, TRZEvent>()
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.EventType.ToTRZEventType()))
                .ForMember(dest => dest.IsHospital, opt => opt.MapFrom(src => src.EventType.IsHospital()))
                .ReverseMap()
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.EventType.ToEventType()));

            CreateMap<TRZEvent, Absence>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId))
                .ForMember(dest => dest.Payroll, opt => opt.Ignore())
                .ForMember(dest => dest.AbsenceType, opt => opt.MapFrom(src => src.EventType.ToEventType()))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => src.Duration))
                .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Note))
                .ForMember(dest => dest.ExportStatus, opt => opt.MapFrom(_ => AbsenceExportStatus.Exported))
                .ReverseMap()
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.AbsenceType.ToTRZEventType()));

            CreateMap<TRZEvent, Hospital>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId))
                .ForMember(dest => dest.Payroll, opt => opt.Ignore())
                .ForMember(dest => dest.HospitalType, opt => opt.MapFrom(src => src.EventType.ToEventType()))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Note))
                .ForMember(dest => dest.ExportStatus, opt => opt.MapFrom(_ => AbsenceExportStatus.Exported))
                .ReverseMap()
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.HospitalType.ToTRZEventType()));

            CreateMap<TRZEventDTO, EventDTO>()
                .ForMember(dest => dest.TRZEventId, opt => opt.MapFrom(src => src.TRZEventId))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.SickNote, opt => opt.MapFrom(src => src.AbsenseExportDetails == null ? "" : src.AbsenseExportDetails.SickNote))
                .ForMember(dest => dest.Note, opt => opt.MapFrom(src => src.Note))
                .ForMember(dest => dest.WorkTimePayrollId, opt => opt.MapFrom(src => src.Payroll == null ? null : src.Payroll.WorkTimeId))
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.EventType.ToEventType()))
                .ReverseMap();

            CreateMap<AbsenceDTO, Absence>()
                .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId))
                .ForMember(dest => dest.AbsenceType, opt => opt.MapFrom(src => src.EventType))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Note))
                .ReverseMap();

            CreateMap<EventDTO, AbsenceDTO>();

            CreateMap<HospitalDTO, Hospital>()
                .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId))
                .ForMember(dest => dest.HospitalType, opt => opt.MapFrom(src => src.EventType))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                .ForMember(dest => dest.SickNote, opt => opt.MapFrom(src => src.SickNote))
                .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Note))
                .ReverseMap();

            CreateMap<EventDTO, HospitalDTO>();

            CreateMap<StructureLevel, TRZDepartmentDTO>()
                .ForMember(dest => dest.Identifier, opt => opt.MapFrom(src => src.TRZId))
                .ForMember(dest => dest.WorkTimeId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.SubGroups, opt => opt.MapFrom(src => src.ChildStructureLevels))
                .ReverseMap();

            CreateMap<EditCompanyRequest, CompanyDTO>();

            CreateMap<EventDTO, Absence>()
                    .ForMember(dest => dest.AbsenceType, opt => opt.MapFrom(src => (EventType)src.TRZEventId))
                    .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                    .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                    .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Note))
                    .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId ?? Guid.Empty))
                    .ForMember(dest => dest.AbsenceType, opt => opt.MapFrom(src => src.EventType))
                    .ForMember(dest => dest.Payroll, opt => opt.Ignore())
                .ReverseMap()
                    .ForMember(dest => dest.TRZEventId, opt => opt.MapFrom(src => (int)src.AbsenceType))
                    .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.FromDate))
                    .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.ToDate))
                    .ForMember(dest => dest.Note, opt => opt.MapFrom(src => src.Reference))
                    .ForMember(dest => dest.WorkTimePayrollId, opt => opt.MapFrom(src => src.PayrollId))
                    .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.AbsenceType))
                    .ForMember(dest => dest.SickNote, opt => opt.Ignore());

            CreateMap<EventDTO, Hospital>()
                    .ForMember(dest => dest.HospitalType, opt => opt.MapFrom(src => (EventType)src.TRZEventId))
                    .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                    .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                    .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.EventType.IsHospital() ? null : src.Note))
                    .ForMember(dest => dest.SickNote, opt => opt.MapFrom(src => src.EventType.IsHospital() ? src.Note : null))
                    .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId ?? Guid.Empty))
                    .ForMember(dest => dest.HospitalType, opt => opt.MapFrom(src => src.EventType))
                    .ForMember(dest => dest.Payroll, opt => opt.Ignore())
                .ReverseMap()
                    .ForMember(dest => dest.TRZEventId, opt => opt.MapFrom(src => (int)src.HospitalType))
                    .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.FromDate))
                    .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.ToDate))
                    .ForMember(dest => dest.Note, opt => opt.MapFrom(src => src.Reference))
                    .ForMember(dest => dest.SickNote, opt => opt.MapFrom(src => src.SickNote))
                    .ForMember(dest => dest.WorkTimePayrollId, opt => opt.MapFrom(src => src.PayrollId))
                    .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.HospitalType));

            CreateMap<Absence, AbsenceHospitalDTO>()
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.AbsenceType.GetDescription()))
                .ForMember(dest => dest.TypeIdentifier, opt => opt.MapFrom(src => src.AbsenceType))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.FromDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.ToDate))
                .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Reference))
                .ForMember(dest => dest.ExportStatus, opt => opt.MapFrom(src => src.ExportStatus))
                .ForMember(dest => dest.SickNote, opt => opt.Ignore());

            CreateMap<Hospital, AbsenceHospitalDTO>()
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.HospitalType.GetDescription()))
                .ForMember(dest => dest.TypeIdentifier, opt => opt.MapFrom(src => src.HospitalType))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.FromDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.ToDate))
                .ForMember(dest => dest.SickNote, opt => opt.MapFrom(src => src.SickNote))
                .ForMember(dest => dest.ExportStatus, opt => opt.MapFrom(src => src.ExportStatus))
                .ForMember(dest => dest.Reference, opt => opt.Ignore());

            CreateMap<EventDTO, AbsenceHospitalDTO>()
                .ForMember(dest => dest.PayrollId, opt => opt.MapFrom(src => src.WorkTimePayrollId ?? Guid.Empty))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.EventType.GetDescription()))
                .ForMember(dest => dest.TypeIdentifier, opt => opt.MapFrom(src => src.EventType))
                .ForMember(dest => dest.FromDate, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.ToDate, opt => opt.MapFrom(src => src.EndDate))
                .ForMember(dest => dest.Reference, opt => opt.MapFrom(src => src.Note))
                .ForMember(dest => dest.SickNote, opt => opt.MapFrom(src => src.SickNote))
                .ForMember(dest => dest.IsOverlapping, opt => opt.Ignore());

            CreateMap<RoleEmployeeDTO, RoleEmployee>().ReverseMap();
            CreateMap<Role, RoleDTO>().ReverseMap();
            CreateMap<Permission, PermissionDTO>().ReverseMap();

            CreateMap<AddAbsenceRequest, EventDTO>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Note, opt => opt.MapFrom(src => src.Comment))
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => src.TypeIdentifier))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.ToDate))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.FromDate))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.WorkTimePayrollId, opt => opt.Ignore());

            CreateMap<Notification, NotificationDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.Employee.UserId))
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.Employee.CompanyId))
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.Url))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.NotificationType.Name))
                .ForMember(dest => dest.Payload, opt => opt.MapFrom(src => src.Payload));

            CreateMap<NotificationSettingDTO, NotificationSetting>().ReverseMap();
            CreateMap<Role, Common.DTOs.Roles.RoleDTO>().ReverseMap();
            CreateMap<Permission, Common.DTOs.Roles.PermissionDTO>().ReverseMap();
            CreateMap<NotificationTypeDTO, NotificationType>().ReverseMap();

            CreateMap<EditAbsenceRequest, EventDTO>()
                .ForMember(dest => dest.WorkTimePayrollId, opt => opt.MapFrom(src => src.PayrollId))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.ToDate))
                .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.FromDate))
                .ForMember(dest => dest.EventType, opt => opt.MapFrom(src => (EventType)src.TypeIdentifier));

            CreateMap<AnnexPayroll, LightAnnexPayrollDTO>()
                .ForMember(dest => dest.MainPayrollWorktimeGuid, opt => opt.MapFrom(src => src.MainPayroll.Id))
                .ForMember(dest => dest.AnnexPayrollFromDate, opt => opt.MapFrom(src => src.FromDate))
                .ForMember(dest => dest.AnnexPayrollToDate, opt => opt.MapFrom(src => src.ToDate));

            CreateMap<Payroll, LightPayrollDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Employee, opt => opt.MapFrom(src => src.Employee))
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
                .ForMember(dest => dest.ContractNumber, opt => opt.MapFrom(src => src.ContractNumber))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src =>
                    new NomenclatureDTO
                    {
                        Name = ((src.AnnexPayrolls != null && src.AnnexPayrolls.Any())
                            ? src.AnnexPayrolls
                                .OrderBy(a => a.FromDate ?? DateTime.MinValue)
                                .LastOrDefault()!.Position
                            : null) ?? src.Position
                    }))
                .ForMember(dest => dest.StructureLevelId, opt => opt.MapFrom(src => src.StructureLevelId))
                .ForMember(dest => dest.ContractType, opt => opt.MapFrom(src => (int?)src.ContractType))
                .ForMember(dest => dest.Leaves, opt => opt.Ignore())
                .ForMember(dest => dest.AnnexPayrolls, opt => opt.MapFrom(src => src.AnnexPayrolls))
                .AfterMap((src, dest, context) =>
                {
                    var leaves = new List<AbsenceHospitalDTO>();
                    leaves.AddRange(context.Mapper.Map<IEnumerable<AbsenceHospitalDTO>>(src.Absences));
                    leaves.AddRange(context.Mapper.Map<IEnumerable<AbsenceHospitalDTO>>(src.Hospitals));
                    dest.Leaves = leaves;
                });

            CreateMap<Employee, LightEmployeeDTO>()
              .ForMember(dest => dest.WorkTimeId, opt => opt.MapFrom(src => src.Id));

            CreateMap<EmployeeAddressDTO, Address>()
                .ForMember(dest => dest.City, opt => opt.Ignore())
                .ForMember(dest => dest.District, opt => opt.Ignore())
                .ForMember(dest => dest.Municipality, opt => opt.Ignore()).ReverseMap();

            CreateMap<EmployeePropertyEdit, EmployeePropertyEditDTO>()
                .ReverseMap();
        }
    }
}
