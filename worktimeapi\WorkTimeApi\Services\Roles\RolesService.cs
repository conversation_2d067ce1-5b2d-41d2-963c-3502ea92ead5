﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Roles;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Common.Requests.Roles;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Database.Repositories.Interfaces.Roles;
using WorkTimeApi.Database.Repositories.Interfaces.Users;
using WorkTimeApi.Services.Interfaces.Notifications;
using WorkTimeApi.Services.Interfaces.Roles;

namespace WorkTimeApi.Services.Roles
{
    public class RolesService(
        IRolesRepository rolesRepository,
        IEmployeesRepository employeeRepository,
        IUserRepository userRepository,
        IConfiguration configuration,
        INotificationsService notificationsService,
        IMapper mapper) : IRolesService
    {
        private readonly Guid _defaultCompanyId = Guid.Parse(configuration["WorkTimeDefaultCompanyId"] ?? throw new Exception("Не е въведен default id на WorkTimeDefaultCompanyId в appsettings"));

        public async Task<EmployeeDTO> AddRoleToEmployeeAsync(ShareCompanyRequest shareCompanyRequest)
        {
            var employee = await employeeRepository.GetUserEmployeeAsync(shareCompanyRequest.UserId, shareCompanyRequest.CompanyId);

            var role = await rolesRepository.AddRoleToEmployeeAsync(shareCompanyRequest.RoleId, employee.Id, shareCompanyRequest.CompanyId);

            await notificationsService.InitNotificationSettingsForEmployeeAsync(employee.Id, role.PermissionRoles.Select(r => r.Permission.Id));

            return mapper.Map<EmployeeDTO>(employee);
        }

        public async Task AddRoleToEmployeeAsync(Guid employeeId, Guid roleId)
        {
            var role = await rolesRepository.AddRoleToEmployeeAsync(employeeId, roleId);

            if (role is null)
                return;

            await notificationsService.InitNotificationSettingsForEmployeeAsync(employeeId, role.PermissionRoles.Select(r => r.Permission.Id));
        }

        public async Task<IEnumerable<EmployeeDTO>> GetCompaniesWithEmployeesByPermissionAsync(Guid userId, string permission)
        {
            var comapniesIds = await rolesRepository.GetCompaniesIdsAsync(userId, permission);

            var employees = await employeeRepository.GetEmployeeCompaniesAsync(userId, comapniesIds);

            return mapper.Map<IEnumerable<EmployeeDTO>>(employees);
        }

        public async Task<List<RoleDTO>> GetGeneralRolesAsync()
        {
            var roles = await rolesRepository.GetGeneralRolesAsync(_defaultCompanyId);

            return mapper.Map<List<RoleDTO>>(roles);
        }

        public async Task UpdateEmployeeRoleAsync(ChangeEmployeeRoleRequest request)
        {
            if (request.OldRoleId is null)
            {
                var role = await rolesRepository.AddRoleToEmployeeAsync(request.NewRoleId, request.UserId, request.CompanyId);
                if (role is null)
                    return;

                var employee = await employeeRepository.GetUserEmployeeAsync(request.UserId, request.CompanyId);
                if (employee is null)
                    return;

                await notificationsService.InitNotificationSettingsForEmployeeAsync(employee.Id, role.PermissionRoles.Select(r => r.Permission.Id));
            }
            else
            {
                var roleEmployee = await rolesRepository.ChangeEmployeeRoleAsync(request.UserId, request.NewRoleId, request.OldRoleId, request.CompanyId);
                if (roleEmployee is null)
                    return;

                await notificationsService.InitNotificationSettingsForEmployeeAsync(roleEmployee.EmployeeId, roleEmployee.Role.PermissionRoles.Select(r => r.Permission.Id));
            }
        }

        public async Task<bool> SetWorkTimeRoleAsync(SetWorkTimeRoleRequest setWorkTimeRoleRequest)
        {
            var user = await userRepository.GetUserAsync(setWorkTimeRoleRequest.UserId);
            var role = await rolesRepository.GetRoleByIdAsync(setWorkTimeRoleRequest.RoleId);

            if (user == null || role == null)
                return false;

            user.WorkTimeRoleId = role.Id;
            await userRepository.UpdateUserAsync(user);

            return true;
        }

        public async Task<RoleDTO?> GetWorkTimeRoleAsync(Guid userId)
        {
            var user = await userRepository.GetUserAsync(userId);

            return mapper.Map<RoleDTO>(user?.WorkTimeRole);
        }

        public async Task<List<RoleDTO>> GetUserEmployeeRoleAsync(Guid userId, Guid companyId)
        {
            var employee = await employeeRepository.GetUserEmployeeAsync(userId, companyId);

            var roles = await employeeRepository.GetEmployeeRoles(employee.Id);

            return mapper.Map<List<RoleDTO>>(roles);
        }

        public async Task DeletePendingRoleEmployee(string email, Guid companyId)
        {
            await rolesRepository.DeletePendingRoleEmployee(email, companyId);
        }
    }
}
