using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Payrolls;
using Microsoft.EntityFrameworkCore;
using WorkTimeApi.Database.Models.TRZ;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Database.Repositories.Payrolls
{
    public class PayrollsRepository(IDbContextFactory<WorkTimeApiDbContext> contextFactory)
        : BaseRepository<Payroll>(contextFactory), IPayrollsRepository
    {
        public async Task<Payroll> AddPayrollAsync(Payroll payroll)
        {
            using var context = await contextFactory.CreateDbContextAsync();
            context.Payrolls.Add(payroll);
            await context.SaveChangesAsync();

            return payroll;
        }

        public async Task<IEnumerable<Payroll>> GetPayrollsAsync(Guid companyId)
        {
            using var context = await contextFactory.CreateDbContextAsync();

            var payrolls = await context
                .Payrolls
                .Include(p => p.Employee)
                .Include(p => p.Absences.Where(a => a.Status != AbsenceStatus.Declined
                        && a.Status != AbsenceStatus.Deleted
                        && a.Status != AbsenceStatus.DeletedByAdmin))
                .Include(p => p.Hospitals.Where(h => h.Status != AbsenceStatus.Declined
                        && h.Status != AbsenceStatus.Deleted
                        && h.Status != AbsenceStatus.DeletedByAdmin))
                .Include(p => p.AnnexPayrolls)
                .Where(p => p.CompanyId == companyId && p.ContractType.HasValue
                    && (p.ContractType.Value == TypeOfAppointment.EmploymentContracts ||
                    p.ContractType.Value == TypeOfAppointment.ManagementContracts ||
                    p.ContractType.Value == TypeOfAppointment.SelfEmployedOwner ||
                    p.ContractType.Value == TypeOfAppointment.CivilContractStandart ||
                    p.ContractType.Value == TypeOfAppointment.CivilContractOther ||
                    p.ContractType.Value == TypeOfAppointment.EmploymentRelationship ||
                    p.ContractType.Value == TypeOfAppointment.RepresentativeOfAnExternalCompany))
                .ToListAsync();

            return payrolls;
        }

        public async Task<List<Payroll>?> GetPayrollsByEmployeeIdAsync(Guid Id)
        {
            using var context = await contextFactory.CreateDbContextAsync();

            return await context
              .Payrolls
              .Where(p => p.EmployeeId == Id)
              .Include(p=>p.AnnexPayrolls)
              .ToListAsync();
        }

        public async Task DeletePayrollAsync(string payrollId)
        {
            using var context = await contextFactory.CreateDbContextAsync();
            var payroll = await context.Payrolls.FindAsync(payrollId);

            if (payroll != null)
            {
                context.Payrolls.Remove(payroll);
                await context.SaveChangesAsync();
            }
            else
            {
                throw new Exception("Неуспешно изтриване!");
            }
        }

        public async Task<List<TRZPayroll>> GetPendingPayrollsAsync(Guid companyId)
        {
            using var context = await contextFactory.CreateDbContextAsync();

            return await context
                .TRZPayrolls
                .Include(p => p.Employee)
                .Where(p => p.CompanyId == companyId)
                .ToListAsync();
        }

        public async Task<Payroll?> GetPayrollAsync(Guid? id)
        {
            if (id == Guid.Empty)
                return null;

            using var context = await contextFactory.CreateDbContextAsync();

            return await context.Payrolls
                .Include(p => p.Employee)
                .Include(p=>p.AnnexPayrolls)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Payroll?> GetPayrollWithAnnexPayrollsAsync(Guid payrollId)
        {
            using var context = await contextFactory.CreateDbContextAsync();

            return await context.Payrolls
                .Include(p => p.Employee)
                    .ThenInclude(e => e.LengthsOfService)
                .Include(p => p.StructureLevel)
                .Include(p => p.AnnexPayrolls)
                    .ThenInclude(ap => ap.StructureLevel)
                .Include(p => p.AnnexPayrolls)
                    .ThenInclude(ap => ap.AnnexPayrollKIDs)
                        .ThenInclude(ap => ap.KID)
                .Include(p => p.AnnexPayrolls)
                    .ThenInclude(ap => ap.AnnexPayrollKPDs)
                        .ThenInclude(ap => ap.NKPD)
                .Include(p => p.PayrollKPDs)
                    .ThenInclude(p => p.NKPD)
                .Include(p => p.PayrollKIDs)
                    .ThenInclude(p => p.KID)
                .FirstOrDefaultAsync(p => p.Id == payrollId);
        }

        public async Task UpdateAdditionalTermsAsync(Guid payrollId, string additionalTerms)
        {
            using var context = await contextFactory.CreateDbContextAsync();

            var payroll = await context.Payrolls
                .FirstOrDefaultAsync(p => p.Id == payrollId);

            if (payroll == null)
            {
                throw new Exception("Неуспешна промяна на допълнителните условия!");
            }

            payroll.AdditionalTerms = additionalTerms;

            await context.SaveChangesAsync();
        }
    }
}
