import { LengthOfServiceDTO } from "../models/DTOs/payrolls/LengthOfService";
import { PayrollSummaryDTO } from "../models/DTOs/payrolls/PayrollSummaryDTO";
import { AnnexPayrollSummaryDTO } from "../models/DTOs/payrolls/AnnexPayrollSummaryDTO";

export const calculateExperienceFromDates = (
  fromDate: Date | null,
  toDate: Date | null
): LengthOfServiceDTO | null => {
  if (!fromDate) return null;

  const startDate = new Date(fromDate);
  const endDate = toDate ? new Date(toDate) : new Date();

  if (startDate > endDate) return null;

  let years = endDate.getFullYear() - startDate.getFullYear();
  let months = endDate.getMonth() - startDate.getMonth();
  let days = endDate.getDate() - startDate.getDate();

  if (days < 0) {
    months--;
    const lastDayOfPrevMonth = new Date(
      endDate.getFullYear(),
      endDate.getMonth(),
      0
    ).getDate();
    days += lastDayOfPrevMonth;
  }

  if (months < 0) {
    years--;
    months += 12;
  }

  if (days >= 30) {
    const additionalMonths = Math.floor(days / 30);
    months += additionalMonths;
    days = days % 30;
  }

  if (months >= 12) {
    const additionalYears = Math.floor(months / 12);
    years += additionalYears;
    months = months % 12;
  }

  return {
    id: "",
    employeeId: "",
    years: years,
    months: months,
    days: days,
  };
};

export const getProfessionalExperienceInCompany = (
  data: PayrollSummaryDTO | AnnexPayrollSummaryDTO,
  effectiveToDate?: Date | null
): LengthOfServiceDTO | null => {
  if (
    "professionalЕxperienceInCompany" in data &&
    data.professionalЕxperienceInCompany
  ) {
    return data.professionalЕxperienceInCompany;
  }

  const toDate = effectiveToDate !== undefined ? effectiveToDate : data.toDate;
  return calculateExperienceFromDates(data.fromDate, toDate);
};
