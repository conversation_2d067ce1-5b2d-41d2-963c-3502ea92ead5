import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import styled from "styled-components";
import BackgroundImage from "../../assets/images/background-image.svg";
import LinkExpiredIcon from "../../assets/images/errors/link-expired.svg";
import ServerErrorIcon from "../../assets/images/errors/no-server-connection.svg";
import SuccessIcon from "../../assets/images/success-icon.svg";
import { confirmEmail } from "../../services/authentication/authenticationService";
import Translator from "../../services/language/Translator";

enum ConfirmationStatus {
  LOADING = "LOADING",
  SUCCESS = "SUCCESS",
  CLIENT_ERROR = "CLIENT_ERROR",
  SERVER_ERROR = "SERVER_ERROR",
}

const MainContainer = styled.div`
  min-height: calc(100vh - 100px);
  background-color: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 546.727px;
    height: 383.518px;
    background-image: url(${BackgroundImage});
    background-size: contain;
    background-repeat: no-repeat;
    background-position: bottom right;
    pointer-events: none;
    z-index: 0;
  }
`;

const ContentContainer = styled.div`
  background-color: transparent;
  text-align: center;
  max-width: 500px;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
`;

const IconContainer = styled.div`
  margin-bottom: 0.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const TextContainer = styled.div<{ color?: string }>`
  font-size: 18px;
  line-height: 24px;
  font-weight: normal;
  letter-spacing: 0px;
  color: ${(props) => props.color || "var(--confirmation-email-sent-color)"};
  opacity: 1;
`;

const ConfirmEmail = () => {
  const [confirmationStatus, setConfirmationStatus] =
    useState<ConfirmationStatus>(ConfirmationStatus.LOADING);
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const userId = queryParams.get("userId");
  const code = queryParams.get("code");

  useEffect(() => {
    if (userId && code) {
      confirmEmail(userId, code)
        .then(() => {
          setConfirmationStatus(ConfirmationStatus.SUCCESS);
        })
        .catch((error: number) => {
          if (error >= 400 && error < 500) {
            setConfirmationStatus(ConfirmationStatus.CLIENT_ERROR);
          } else {
            setConfirmationStatus(ConfirmationStatus.SERVER_ERROR);
          }
        });
    }
  }, [userId, code]);

  const renderContent = () => {
    switch (confirmationStatus) {
      case ConfirmationStatus.SUCCESS:
        return (
          <>
            <IconContainer>
              <img
                src={SuccessIcon}
                alt="Success"
                style={{
                  width: "48px",
                  height: "48px",
                }}
              />
            </IconContainer>
            <TextContainer>
              <Translator getString="E-mail confirmed successfully" />
            </TextContainer>
          </>
        );

      case ConfirmationStatus.CLIENT_ERROR:
        return (
          <>
            <IconContainer>
              <img
                src={LinkExpiredIcon}
                alt="Success"
                style={{
                  width: "48px",
                  height: "48px",
                }}
              />
            </IconContainer>
            <TextContainer>
              <Translator getString="strEmailConfirmationLinkExpired" />
            </TextContainer>
          </>
        );

      case ConfirmationStatus.SERVER_ERROR:
        return (
          <>
            <IconContainer>
              <img
                src={ServerErrorIcon}
                alt="Success"
                style={{
                  width: "48px",
                  height: "48px",
                }}
              />
            </IconContainer>
            <TextContainer>
              <Translator getString="strEmailConfirmationServerError" />
            </TextContainer>
          </>
        );

      case ConfirmationStatus.LOADING:
      default:
        return (
          <TextContainer>
            <Translator getString="Confirming E-mail" />
          </TextContainer>
        );
    }
  };

  return (
    <MainContainer>
      <ContentContainer>{renderContent()}</ContentContainer>
    </MainContainer>
  );
};

export default ConfirmEmail;
