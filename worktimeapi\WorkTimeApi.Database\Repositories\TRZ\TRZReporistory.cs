using Microsoft.EntityFrameworkCore;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.TRZ;
using WorkTimeApi.Database.Repositories.Interfaces.TRZ;

namespace WorkTimeApi.Database.Repositories.TRZ
{
    public class TRZReporistory : ITRZRepository
    {
        private readonly IDbContextFactory<WorkTimeApiDbContext> _dbContextFactory;

        public TRZReporistory(IDbContextFactory<WorkTimeApiDbContext> contextFactory)
        {
            _dbContextFactory = contextFactory;
        }

        public async Task<IEnumerable<Employee>> AddTRZEmployeesAsync(List<Employee> employees)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            //TO DO update existing

            await context.Employees.AddRangeAsync(employees);
            await context.SaveChangesAsync();

            return employees;
        }

        public async Task<Employee> AddTRZEmployeeAsync(Employee employee)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();

                await context.Employees.AddAsync(employee);
                await context.SaveChangesAsync();

                return employee;
            }
            catch (DbUpdateException e)
            {
                //TO DO update existing
                return employee;
            }
        }

        public async Task<IEnumerable<Employee>> AddPenidngEmployeePayrollsAsync(List<Employee> employees, Guid companyId, List<Payroll> payrolls)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();

                foreach (var employee in employees)
                {
                    employee.Status = EmployeeStatus.Active;
                    context.Employees.Attach(employee);
                    context.Entry(employee).Property(e => e.Status).IsModified = true;

                    var currentPayrolls = payrolls.Where(p => p.EmployeeId == employee.Id).ToList();

                    if (currentPayrolls == null)
                        continue;

                    foreach (var payroll in currentPayrolls)
                    {
                        var trackedPayroll = context?.Payrolls.FirstOrDefault(p => p.Id == payroll.Id);

                        if (trackedPayroll != null)
                            context?.Entry(trackedPayroll).CurrentValues.SetValues(payroll);
                        else
                            await context.Payrolls.AddAsync(payroll);

                        foreach (var absence in payroll.Absences)
                        {
                            var trackedAbsence = context?.Absences.FirstOrDefault(p => p.Id == absence.Id);
                            absence.Status = AbsenceStatus.Approved;
                            absence.ExportStatus = AbsenceExportStatus.TRZExported;
                            if (trackedAbsence != null)
                                context?.Entry(trackedAbsence).CurrentValues.SetValues(absence);
                            else
                            {
                                await context.Absences.AddAsync(absence);
                            }
                        }

                        foreach (var hospital in payroll.Hospitals)
                        {
                            var trackedHospital = context?.Hospitals.FirstOrDefault(p => p.Id == hospital.Id);
                            hospital.Status = AbsenceStatus.Approved;
                            hospital.ExportStatus = AbsenceExportStatus.TRZExported;
                            if (trackedHospital != null)
                                context?.Entry(trackedHospital).CurrentValues.SetValues(hospital);
                            else
                            {
                                await context.Hospitals.AddAsync(hospital);
                            }
                        }

                        if (payroll.AnnexPayrolls == null || !payroll.AnnexPayrolls.Any())
                            continue;

                        foreach (var annex in payroll.AnnexPayrolls)
                        {
                            if (trackedPayroll != null)
                            {
                                var trackedAnnex = context?.AnnexPayrolls.FirstOrDefault(a => a.Id == annex.Id);
                                if (trackedAnnex != null)
                                {
                                    annex.PayrollId = trackedAnnex.PayrollId;
                                    context?.Entry(trackedAnnex).CurrentValues.SetValues(annex);
                                    continue;
                                }
                            }

                            annex.PayrollId = payroll.Id;
                            await context.AnnexPayrolls.AddAsync(annex);
                        }
                    }
                }

                await context.SaveChangesAsync();
                return employees;
            }
            catch (DbUpdateException e)
            {
                return null;
            }
        }

        public async Task<IEnumerable<Employee>> AddTRZEmployeesAsync(List<Employee> pendingEmployees, Guid companyId)
        {
            var processed = new List<Employee>();

            foreach (var pendingEmployee in pendingEmployees)
            {
                using (var context = await _dbContextFactory.CreateDbContextAsync())
                {
                    var exists = await context.Employees
                        .AsNoTracking()
                        .AnyAsync(e => (e.Id == pendingEmployee.Id || e.UserId == pendingEmployee.UserId) && e.CompanyId == companyId);

                    if (!exists)
                    {
                        pendingEmployee.Company = await context.Companies.FindAsync(companyId);

                        await context.Employees.AddAsync(pendingEmployee);
                        await context.SaveChangesAsync();

                        processed.Add(pendingEmployee);
                        continue;
                    }

                    var existingEmployee = await context.Employees
                         .Where(e => (e.Id == pendingEmployee.Id || e.UserId == pendingEmployee.UserId) && e.CompanyId == companyId)
                        .Include(e => e.User)
                        .Include(e => e.EmployeeBankAccounts)
                            .ThenInclude(eb => eb.BankAccount)
                        .Include(e => e.LengthsOfService)
                        .Include(e => e.EmployeeAddresses)
                            .ThenInclude(ea => ea.Address)
                        .Include(e => e.PendingPayrolls)
                            .ThenInclude(p => p.AnnexPayrolls)
                        .Include(e => e.PendingPayrolls)
                            .ThenInclude(p => p.Events)
                        .AsSplitQuery() // reduce join explosion
                        .FirstOrDefaultAsync();

                    if (existingEmployee == null)
                    {
                        pendingEmployee.Company = await context.Companies.FindAsync(companyId);
                        await context.Employees.AddAsync(pendingEmployee);
                        await context.SaveChangesAsync();
                        processed.Add(pendingEmployee);
                        continue;
                    }

                    var existingEmployeeBankAccountMappings = context.EmployeeBankAccounts
                         .Where(x => x.EmployeeId == existingEmployee.Id);

                    var bankAccountIdsToRemove = existingEmployeeBankAccountMappings
                        .Select(x => x.BankAccountId)
                        .ToList();

                    if (bankAccountIdsToRemove.Any())
                    {
                        context.EmployeeBankAccounts.RemoveRange(existingEmployeeBankAccountMappings);

                        var bankAccountsToRemove = context.BankAccounts
                            .Where(b => bankAccountIdsToRemove.Contains(b.Id));
                        context.BankAccounts.RemoveRange(bankAccountsToRemove);
                    }

                    if (pendingEmployee.EmployeeBankAccounts != null)
                    {
                        foreach (var bankAccMapping in pendingEmployee.EmployeeBankAccounts)
                        {
                            bankAccMapping.EmployeeId = existingEmployee.Id;
                            bankAccMapping.Employee = null;
                            context.EmployeeBankAccounts.Add(bankAccMapping);
                        }
                    }

                    if (pendingEmployee.EmployeeAddresses != null)
                    {
                        foreach (var pendingAddr in pendingEmployee.EmployeeAddresses)
                        {
                            var existingAddrMapping = existingEmployee.EmployeeAddresses
                                .FirstOrDefault(a => a.AddressId == pendingAddr.Address.Id);

                            if (existingAddrMapping != null && pendingAddr.Address?.Id != Guid.Empty)
                            {
                                context.Entry(existingAddrMapping.Address).CurrentValues.SetValues(pendingAddr.Address);
                            }
                            else
                            {
                                existingEmployee.EmployeeAddresses.Add(pendingAddr);
                            }
                        }
                    }

                    pendingEmployee.UserId = existingEmployee.UserId;

                    UpdateUser(pendingEmployee, existingEmployee.User);

                    var preservedStatus = existingEmployee.Status;
                    pendingEmployee.Id = existingEmployee.Id;
                    context.Entry(existingEmployee).CurrentValues.SetValues(pendingEmployee);

                    existingEmployee.UploadDate = DateTime.UtcNow;
                    existingEmployee.LengthsOfService = pendingEmployee.LengthsOfService;
                    existingEmployee.Status = preservedStatus;

                    if (pendingEmployee.PendingPayrolls != null)
                    {
                        foreach (var pendingPayroll in pendingEmployee.PendingPayrolls)
                        {
                            pendingPayroll.EmployeeId = existingEmployee.Id;

                            var existingPayroll = existingEmployee.PendingPayrolls
                                .FirstOrDefault(p => p.Id == pendingPayroll.Id);

                            if (pendingPayroll.Events != null && pendingPayroll.Events.Count > 1)
                            {
                                var eventsWithValidIds = pendingPayroll.Events
                                    .Where(a => a.Id != Guid.Empty)
                                    .ToList();

                                var eventsWithoutValidIds = pendingPayroll.Events
                                    .Where(a => a.Id == Guid.Empty)
                                    .ToList();

                                var mergedEvents = new List<TRZEvent>();

                                if (eventsWithValidIds.Count > 1)
                                {
                                    var groupedEvents = eventsWithValidIds
                                        .GroupBy(a => a.Id)
                                        .Select(g =>
                                        {
                                            if (g.Count() == 1)
                                                return g.First();

                                            var absence = g.First();
                                            absence.StartDate = g.Min(a => a.StartDate);
                                            absence.EndDate = g.Max(a => a.EndDate);
                                            absence.Duration = g.Sum(a => a.Duration ?? 0);
                                            absence.EventType = TRZEventType.ПлатенГодишенОтпуск;
                                            return absence;
                                        })
                                        .ToList();

                                    mergedEvents.AddRange(groupedEvents);
                                }
                                else if (eventsWithValidIds.Count == 1)
                                {
                                    mergedEvents.Add(eventsWithValidIds.First());
                                }

                                mergedEvents.AddRange(eventsWithoutValidIds);

                                pendingPayroll.Events.Clear();
                                foreach (var mergedEvent in mergedEvents)
                                    pendingPayroll.Events.Add(mergedEvent);
                            }

                            if (existingPayroll == null)
                            {
                                pendingPayroll.EmployeeId = existingEmployee.Id;
                                await context.TRZPayrolls.AddAsync(pendingPayroll);
                                continue;
                            }

                            context.Entry(existingPayroll).CurrentValues.SetValues(pendingPayroll);
                            existingPayroll.UploadDate = DateTime.UtcNow;

                            if (pendingPayroll.AnnexPayrolls != null)
                            {
                                foreach (var pendingAnnex in pendingPayroll.AnnexPayrolls)
                                {
                                    var existingAnnex = existingPayroll.AnnexPayrolls
                                        ?.FirstOrDefault(a => a.Id == pendingAnnex.Id);

                                    if (existingAnnex == null)
                                    {
                                        pendingAnnex.TRZPayrollId = existingPayroll.Id;
                                        await context.TRZAnnexPayrolls.AddAsync(pendingAnnex);
                                        continue;
                                    }

                                    context.Entry(existingAnnex).CurrentValues.SetValues(pendingAnnex);
                                    existingAnnex.UploadDate = DateTime.UtcNow;
                                }
                            }

                            if (pendingPayroll.Events != null)
                            {
                                foreach (var pendingEvent in pendingPayroll.Events)
                                {
                                    var existingEvent = existingPayroll.Events
                                        ?.FirstOrDefault(ev => ev.Id == pendingEvent.Id);

                                    if (existingEvent == null)
                                    {
                                        pendingEvent.WorkTimePayrollId = pendingPayroll.Id;
                                        await context.TRZEvents.AddAsync(pendingEvent);
                                        continue;
                                    }

                                    context.Entry(existingEvent).CurrentValues.SetValues(pendingEvent);
                                    existingEvent.WorkTimePayrollId = pendingPayroll.Id;
                                }
                            }
                        }
                    }

                    await context.SaveChangesAsync();

                    processed.Add(pendingEmployee);
                }
            }

            return processed;
        }

        private void UpdateUser(Employee pendingEmployee, User user)
        {
            user.Email = pendingEmployee.Email;
            user.FirstName = pendingEmployee.FirstName;
            user.SecondName = pendingEmployee.SecondName;
            user.LastName = pendingEmployee.LastName;
        }

        public async Task<IEnumerable<Employee>> GetPendingEmployeesAsync(List<Guid> payrollIds)
        {
            await using var context = await _dbContextFactory.CreateDbContextAsync();

            var employees = await context.Employees
                .Where(e => e.PendingPayrolls.Any(pp => payrollIds.Contains(pp.Id)))
                .Include(e => e.PendingPayrolls.Where(pp => payrollIds.Contains(pp.Id)))
                    .ThenInclude(pp => pp.AnnexPayrolls)
                    .ThenInclude(pp => pp.KPDs)
                .Include(e => e.PendingPayrolls.Where(pp => payrollIds.Contains(pp.Id)))
                    .ThenInclude(pp => pp.AnnexPayrolls)
                    .ThenInclude(pp => pp.KIDs)
                .Include(e => e.PendingPayrolls.Where(pp => payrollIds.Contains(pp.Id)))
                    .ThenInclude(pp => pp.Events)
                .Include(e => e.PendingPayrolls.Where(pp => payrollIds.Contains(pp.Id)))
                    .ThenInclude(pp => pp.KIDs)
                .Include(e => e.PendingPayrolls.Where(pp => payrollIds.Contains(pp.Id)))
                    .ThenInclude(pp => pp.KPDs)
                .AsSplitQuery()
                .ToListAsync();

            return employees;
        }

        public async Task<IEnumerable<TRZEvent>> AddTRZPendingEventsAsync(List<TRZEvent> pendingEvents)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            foreach (var pendingEvent in pendingEvents)
            {
                if (pendingEvent.Id != Guid.Empty)
                {
                    var dbEvent = await context.TRZEvents.FirstOrDefaultAsync(e => e.Id == pendingEvent.Id);
                    if (dbEvent != null)
                        context.Entry(dbEvent).CurrentValues.SetValues(pendingEvent);

                    continue;
                }

                await context.TRZEvents.AddAsync(pendingEvent);
            }

            await context.SaveChangesAsync();

            return pendingEvents;
        }

        public async Task<IEnumerable<TRZEvent>> GetTRZEventsAsync(Guid payrollId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            return await context.TRZEvents.Where(e => e.WorkTimePayrollId == payrollId).ToListAsync();
        }

        public async Task<Absence> AddTRZPendingAbsencesAsync(Absence absence)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            await context.Absences.AddAsync(absence);
            await context.SaveChangesAsync();

            return absence;
        }

        public async Task<Absence> ImportAbsenceAsync(Absence absence)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            await context.Absences.AddAsync(absence);
            await context.SaveChangesAsync();

            return absence;
        }

        public async Task<Hospital> ImportHospitalAsync(Hospital hospital)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            await context.Hospitals.AddAsync(hospital);
            await context.SaveChangesAsync();

            return hospital;
        }

        public async Task DeletePendingEventAsync(Guid pendingEventId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var pendingEvent = await context.TRZEvents.FindAsync(pendingEventId);

            if (pendingEvent != null)
            {
                context.TRZEvents.Remove(pendingEvent);
                await context.SaveChangesAsync();
            }
            else
            {
                throw new Exception("Неуспешно изтриване!");
            }
        }

        public async Task<IEnumerable<Employee>> GetTRZEmployeesAsync(Guid companyId)
        {
            await using var context = await _dbContextFactory.CreateDbContextAsync();

            return await context.Employees
                .Where(e => e.PendingPayrolls.Any(tp => tp.CompanyId == companyId)).ToListAsync();
        }

        public async Task<IEnumerable<TRZPayroll>> GetTRZPayrollsAsync(Guid companyId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            return await context.TRZPayrolls.Where(e => e.CompanyId == companyId).ToListAsync();
        }

        public async Task DeletePendingPayrollsAsync(Guid pendingEmployeeId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            var payrolls = context.TRZPayrolls.Where(p => p.Id == pendingEmployeeId).ToList();
            context.TRZPayrolls.RemoveRange(payrolls);

            await context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Employee>> GetPendingEmployeePayrollListAsync(Guid companyId)
        {
            await using var context = await _dbContextFactory.CreateDbContextAsync();

            var result = await context.Employees
                .Include(e => e.PendingPayrolls)
                    .ThenInclude(pp => pp.AnnexPayrolls)
                .Where(e => e.CompanyId == companyId)
                .Where(e => e.PendingPayrolls.Any())
                .ToListAsync();

            return result;
        }

        public async Task<Employee> GetPendingEmployeeByWorktimeId(Guid worktimeId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            var employee = await context.Employees
                .Include(e => e.PendingPayrolls)
                .ThenInclude(p => p.AnnexPayrolls)
                .FirstOrDefaultAsync(e => e.Id == worktimeId);

            if (employee == null)
                throw new Exception("Служителят вече е добавен/изтрит!");

            return employee;
        }
    }
}
