{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"SSOConnection": "Server=.; Database=microinvest-sso; User Id=sa; Password=***********; MultipleActiveResultSets=true; TrustServerCertificate=True"}, "UserRegistrationsBaseUrl": "http://localhost:52242/", "JwtSecret": "5ef613751b0c4104b0e6999d2824b8c3e86a19c142b5426da2ebd3e1069d658d743a0e1d58e8453db1e55aab7d1b2312835ce97cc6e94d088ed682e9af021ee587f68cc7336d4a91895daeb23cdca4695267711feab443a4a843023fb1adaf1f", "JwtIssuer": "Microinvest.SSO", "JwtAudience": "ALL", "GoogleAuth": {"REDIRECT_URL": "http://localhost:3000"}}