import { useEffect, useState } from "react";
import { GetCoworkersResponse } from "../../models/Responses/GetCoworkersResponse";
import { authenticatedGet } from "../../services/connectionService";
import Container from "../../components/Container";
import { useAppSelector } from "../../app/hooks";
import { companiesState } from "../companies/companiesActions";
import { CoworkerDTO } from "../../models/DTOs/employees/CoworkerDTO";
import { RoleDTO } from "../../models/DTOs/RoleDTO";
import { getRoles } from "../../services/authorization/authorizationService";
import Dropdown from "../../components/Dropdown/Dropdown";
import styled, { css, keyframes } from "styled-components";
import upImage from "../../assets/images/button/up.png";
import { translate } from "../../services/language/Translator";
import { toast } from "react-toastify";
import { changeUserRole } from "../../services/companies/companiesService";
import { declinePendingEmployee } from "../../services/employees/employeesService";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";

const HeaderWrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1rem 1rem 1rem 1.5rem;
  transition: 0.4s;
  background: var(--company-dropdown-opened-color);
  outline: none;
  cursor: pointer;
  font-weight: bold;
  &:first-child {
    border-radius: ${(props) =>
      props.isOpen ? "1.625rem 1.625rem 0 0" : "2rem"};
  }
`;

const HeaderImage = styled(Container)<{ isClicked: boolean }>`
  position: absolute;
  background-size: cover;
  height: 1rem;
  width: 1rem;
  right: 1.5rem;
  top: 40%;
  cursor: pointer;
  background-image: url(${upImage});
  transition-duration: 0.5s;
  ${({ isClicked }) =>
    !isClicked &&
    `transform:rotate(180deg);
    transition-duration: 0.5s;
    background-image: url(${upImage});
    top: 45%;
  `}
`;

const DropdownBody = styled(Dropdown.Body)`
  width: 100%;
  max-height: 30rem;
  overflow-y: auto;
  scrollbar-width: none;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: 10px;
    border: 2px solid var(--scrollbar-track-color);
  }
`;
const Wrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  padding: 0.5rem 1rem 0.5rem 1.5rem;
  background: var(--textbox-color);
  outline: none;
  cursor: pointer;
  &:last-child {
    border-radius: 0 0 2rem 2rem;
    padding-bottom: 2rem;
  }

  animation: ${({ isOpen }) =>
    isOpen
      ? css`
          ${fadeIn} 0.3s ease-in
        `
      : css`
          ${fadeOut} 0.3s ease-out
        `};
`;

const fadeIn = keyframes`
  from {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
    to {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.625rem;
    }
`;

const fadeOut = keyframes`
  from {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.188rem;
    }
    to {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
`;

const EmployeeCard = styled(Container)`
  background: var(--textbox-color);
  border-radius: 1rem;
  padding: 1rem;
  padding-top: 0.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
`;

const InfoRow = styled(Container)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;

  &:last-child {
    margin-bottom: 1rem;
  }
`;

const Label = styled(Container)`
  color: var(--text-secondary-color, #666);
  font-size: 0.9rem;
  font-weight: 500;
`;

const Value = styled(Container)`
  font-weight: 600;
  color: var(--text-primary-color, #333);
  text-align: right;
  max-width: 60%;
  word-break: break-word;
`;

const IconButton = styled.button`
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
`;

const Divider = styled.hr`
  border: none;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 0, 0, 0.1),
    transparent
  );
  margin: 1.5rem 0;
  width: 100%;
`;

interface CoworkersSideMenuProps {}

const CoworkersSideMenu: React.FC<CoworkersSideMenuProps> = ({}) => {
  const [pendingEmployees, setPendingEmployees] = useState<EmployeeDTO[]>([]);
  const [coworkers, setCoworkers] = useState<CoworkerDTO[]>([]);
  const [roles, setRoles] = useState([] as RoleDTO[]);

  const [openStates, setOpenStates] = useState<Record<string, boolean>>({});
  const [selectedRoles, setSelectedRoles] = useState<Record<string, RoleDTO>>(
    {}
  );

  const { activeCompanies } = useAppSelector(companiesState);

  const loadCompanies = async () => {
    const getPendingCoworkersResponse =
      await authenticatedGet<GetCoworkersResponse>("employees/coworkers");

    if (getPendingCoworkersResponse) {
      setPendingEmployees(getPendingCoworkersResponse.pendingEmployees ?? []);
      setCoworkers(getPendingCoworkersResponse.coworkers ?? []);
    }
  };

  useEffect(() => {
    loadCompanies();
  }, []);

  useEffect(() => {
    getRoles().then((response: RoleDTO[]) => {
      setRoles(response);
    });
  }, []);

  const handleIsOpened = (id: string, isOpen: boolean) => {
    setOpenStates((prev) => ({ ...prev, [id]: isOpen }));
  };

  const handleUserAccepted = async (employee: EmployeeDTO, role: RoleDTO) => {
    setSelectedRoles((prev) => ({ ...prev, [employee.workTimeId]: role }));
    if (!role) {
      return;
    }

    await changeUserRole(
      employee.userId,
      employee.companyId,
      undefined,
      role.id,
      true
    );

    toast.success(translate("strRoleChangedSuccessfully"));
  };

  const handleUserDeclined = async (employee: EmployeeDTO) => {
    await declinePendingEmployee(employee.workTimeId, employee.companyId);
    setPendingEmployees((prev) =>
      prev.filter((e) => e.workTimeId !== employee.workTimeId)
    );
    toast.success(translate("strEmployeeDeclined"));
  };

  const handleChangeRole = async (
    coworker: CoworkerDTO,
    role: RoleDTO,
    company: any
  ) => {
    setSelectedRoles((prev) => ({
      ...prev,
      [coworker.employee.workTimeId]: role,
    }));
    if (!role) {
      return;
    }

    await changeUserRole(
      coworker.employee.workTimeId,
      company.id,
      coworker.roles[0].id,
      role.id,
      false
    );

    toast.success(translate("strRoleChangedSuccessfully"));
  };

  return (
    <Container>
      {pendingEmployees &&
        pendingEmployees.map((pendingEmployee) => {
          const isOpen = openStates[pendingEmployee.workTimeId] || false;
          const selectedRole = selectedRoles[pendingEmployee.workTimeId];

          return (
            <EmployeeCard
              key={pendingEmployee.workTimeId}
              data-testid={`pending-employee-${pendingEmployee.workTimeId}`}
            >
              <Container
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginBottom: "0.5rem",
                }}
              >
                <IconButton
                  aria-label="decline"
                  title={translate("strDecline")}
                  onClick={() => handleUserDeclined(pendingEmployee)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="0.75rem"
                    height="0.75rem"
                    viewBox="0 0 9.778 9.779"
                    aria-hidden="true"
                  >
                    <path
                      d="M8.071,9.486,4.889,6.3,1.706,9.486A1,1,0,0,1,.293,8.071L3.475,4.889.293,1.707A1,1,0,0,1,1.706.293L4.889,3.475,8.071.293A1,1,0,0,1,9.485,1.707L6.3,4.889,9.485,8.071A1,1,0,1,1,8.071,9.486Z"
                      fill="var(--error-color, #f73131)"
                    />
                  </svg>
                </IconButton>
              </Container>
              <InfoRow>
                <Label>{`${translate("strEmployee")}`}:</Label>
                <Value
                  data-testid={`pending-employee-name-${pendingEmployee.workTimeId}`}
                >
                  {pendingEmployee.firstName} {pendingEmployee.lastName}
                </Value>
              </InfoRow>

              <InfoRow>
                <Label>{`${translate("strWaitingApproval")}`}:</Label>
                <Value
                  data-testid={`pending-employee-company-${pendingEmployee.workTimeId}`}
                >
                  {
                    activeCompanies.filter(
                      (company) => company.id === pendingEmployee.companyId
                    )[0].name
                  }
                </Value>
              </InfoRow>

              <Dropdown
                style={{ marginTop: "0.5rem" }}
                isOpened={(isOpen) =>
                  handleIsOpened(pendingEmployee.workTimeId, isOpen)
                }
                data-testid={`pending-employee-dropdown-${pendingEmployee.workTimeId}`}
              >
                <Dropdown.Header>
                  <HeaderWrapper isOpen={isOpen}>
                    <span
                      style={{
                        color: selectedRole
                          ? "inherit"
                          : "var(--input-placeholder-color, #999)",
                      }}
                    >
                      {selectedRole
                        ? translate(selectedRole.name)
                        : translate("strChooseRole")}
                    </span>
                  </HeaderWrapper>
                  <HeaderImage isClicked={isOpen}></HeaderImage>
                </Dropdown.Header>
                <DropdownBody>
                  {roles.map((role) => (
                    <Wrapper
                      isOpen={isOpen}
                      key={role.name}
                      onClick={() => handleUserAccepted(pendingEmployee, role)}
                    >
                      {translate(role.name)}
                    </Wrapper>
                  ))}
                </DropdownBody>
              </Dropdown>
            </EmployeeCard>
          );
        })}
      {pendingEmployees.length > 0 && coworkers.length > 0 && <Divider />}
      {coworkers.map((coworker) => {
        const isOpen = openStates[coworker.employee.workTimeId] || false;
        const selectedRole = selectedRoles[coworker.employee.workTimeId];

        const company = activeCompanies.find(
          (company) => company.id === coworker.companyId
        );

        if (!company) {
          return null;
        }

        return (
          <EmployeeCard
            key={coworker.employee.workTimeId}
            data-testid={`coworker-${coworker.employee.workTimeId}`}
          >
            <InfoRow>
              <Label>{`${translate("strEmployee")}`}:</Label>
              <Value
                data-testid={`coworker-name-${coworker.employee.workTimeId}`}
              >
                {coworker.employee.firstName} {coworker.employee.lastName}
              </Value>
            </InfoRow>

            <InfoRow>
              <Label>{`${translate("strCompanyRoleTxt")}`}:</Label>
              <Value
                data-testid={`coworker-company-${coworker.employee.workTimeId}`}
              >
                {company.name}
              </Value>
            </InfoRow>

            <Dropdown
              style={{ marginTop: "0.5rem" }}
              isOpened={(isOpen) =>
                handleIsOpened(coworker.employee.workTimeId, isOpen)
              }
              data-testid={`coworker-dropdown-${coworker.employee.workTimeId}`}
            >
              <Dropdown.Header>
                <HeaderWrapper isOpen={isOpen}>
                  <span
                    style={{
                      color: selectedRole
                        ? "inherit"
                        : "var(--input-placeholder-color, #999)",
                    }}
                  >
                    {selectedRole
                      ? translate(selectedRole.name)
                      : translate(
                          coworker.roles[0]?.name ?? translate("strChooseRole")
                        )}
                  </span>
                </HeaderWrapper>
                <HeaderImage isClicked={isOpen}></HeaderImage>
              </Dropdown.Header>
              <DropdownBody>
                {roles.map((role) => (
                  <Wrapper
                    isOpen={isOpen}
                    key={role.name}
                    onClick={() => handleChangeRole(coworker, role, company)}
                  >
                    {translate(role.name)}
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </EmployeeCard>
        );
      })}
    </Container>
  );
};

export default CoworkersSideMenu;
