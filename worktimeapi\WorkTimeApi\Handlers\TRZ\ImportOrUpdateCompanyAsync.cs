﻿using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Mappers.Interfaces;
using WorkTimeApi.Models.Mediator;
using WorkTimeApi.Services.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Roles;
using WorkTimeApi.Services.Interfaces.Users;

namespace WorkTimeApi.Handlers.TRZ
{
    public class ImportOrUpdateCompanyAsync(
        ICompaniesService companiesService,
        ITRZMapper trzMapper,
        IMediator mediator,
        IUserService userService,
        IEmployeesService employeesService,
        IRolesService rolesService) : IRequestHandler<ImportTRZCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(ImportTRZCompanyRequest request, CancellationToken cancellationToken)
        {
            var companyDTO = await trzMapper.MapImportCompanyRequestToCompanyDTOAsync(request.ImportCompanyRequest);

            if (request.User is null || companyDTO is null)
                return Results.BadRequest();

            var user = await userService.GetUserAsync(request.User.Id);

            if (user == null)
            {
                var userDTO = new UserDTO
                {
                    Id = request.User.Id,
                    Email = request.User.Email,
                    FirstName = request.User.FirstName,
                    SecondName = request.User.SecondName,
                    LastName = request.User.LastName,
                    HasSignedIn = true,
                    WorkTimeRoleId = new Guid(DefaultWorktimeRole.Owner)
                };

                user = await userService.AddUserAsync(userDTO);
            }
            else if (user.WorkTimeRoleId == null)
            {
                user.WorkTimeRoleId = new Guid(DefaultWorktimeRole.Owner);
                await userService.UpdateUserAsync(user);
            }

            var result = request.ImportCompanyRequest.WorkTimeId is null
                ? await companiesService.CreateCompanyAsync(companyDTO, request.User.Id)
                : await companiesService.UpdateCompanyAsync(request.ImportCompanyRequest.WorkTimeId.Value, companyDTO);

            if (!result.IsSuccess || result.Value == null)
                return Results.BadRequest(result.ValidationError);

            var userEmployee = await employeesService.GetUserEmployeeAsync(user.Id, result.Value.Id);
            if (userEmployee == null)
            {
                var employeeDTO = new EmployeeDTO
                {
                    Email = request.User.Email,
                    FirstName = request.User.FirstName,
                    SecondName = request.User.SecondName,
                    LastName = request.User.LastName,
                    UserId = user.Id,
                    CompanyId = result.Value.Id,
                    Status = Common.Enums.EmployeeStatus.Active,
                    UploadDate = DateTime.Now
                };

                var employee = await employeesService.AddEmployeeAsync(employeeDTO);

                if (employee.Value is not null)
                {
                    await rolesService.AddRoleToEmployeeAsync(employee.Value.WorkTimeId, new Guid(DefaultWorktimeRole.Owner));
                    await rolesService.AddRoleToEmployeeAsync(employee.Value.WorkTimeId, new Guid(DefaultWorktimeRole.Creator));
                }

                mediator.Publish(new AddDefaultNotificationSettings(employee.Value.WorkTimeId, new Guid(DefaultWorktimeRole.Creator)));

            }
            else
                mediator.Publish(new AddDefaultNotificationSettings(userEmployee.WorkTimeId, new Guid(DefaultWorktimeRole.Creator)));

            return result.Match(
                companyDTO => Results.Ok(companyDTO),
                validationErrors => Results.BadRequest(validationErrors));
        }
    }
}
