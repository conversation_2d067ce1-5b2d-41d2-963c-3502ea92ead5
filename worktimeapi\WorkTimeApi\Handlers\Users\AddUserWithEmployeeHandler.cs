﻿using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Requests.Users;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Roles;
using WorkTimeApi.Services.Interfaces.Users;

namespace WorkTimeApi.Handlers.Users
{
    public class AddUserWithEmployeeHandler(IUserService userService, IEmployeesService employeesService, IRolesService rolesService) : IRequestHandler<AddUserWithEmployeeRequest, IResult>
    {
        public async Task<IResult> Handle(AddUserWithEmployeeRequest request, CancellationToken cancellationToken)
        {
            var userDTO = await userService.AddUserAsync(request.User);

            //TO DO SET COMPANY
            var employeeDTO = new EmployeeDTO
            {
                UserId = userDTO.Id,
                FirstName = userDTO.FirstName,
                Email = userDTO.Email,
                SecondName = userDTO.SecondName,
                LastName = userDTO.LastName,
                Status = Common.Enums.EmployeeStatus.Active,
                UploadDate = DateTime.Now,
                CompanyId = new Guid("3D49DD04-8D91-4CC9-D218-08DDAE6B0710")
            };
            var employee = await employeesService.AddEmployeeAsync(employeeDTO);

            if (employee.Value is not null)
            {
                await rolesService.AddRoleToEmployeeAsync(employee.Value.WorkTimeId, new Guid(DefaultWorktimeRole.Owner));
                await rolesService.AddRoleToEmployeeAsync(employee.Value.WorkTimeId, new Guid(DefaultWorktimeRole.Creator));
            }

            return  Results.Ok(userDTO);
        }
    }
}
