﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Database.Repositories.Interfaces.Payrolls;
using WorkTimeApi.Database.Repositories.Payrolls;
using WorkTimeApi.Extensions.MediatorExtensions;
using WorkTimeApi.Services.Interfaces.Payrolls;
using WorkTimeApi.Services.Payrolls;

namespace WorkTimeApi.EndpointDefinitions.Payrolls
{
	public class PayrollsEnpointDefinitions : IEndpointDefinition
	{
		public void DefineEndpoints(WebApplication app)
		{
			app.MediatePost<AddPayrollRequest>("/payrolls/add-payroll")
				.MediateGet<LoadPayrollsRequest>("/{companyId}/payrolls/light/load")
				.AuthenticatedGet<LoadPayrollDataRequest>("/{companyId}/payrolls/payroll-data")
				.AuthenticatedGet<LoadPayrollsPositionNamesRequest>("/{companyId}/payrolls/position-names")
				.MediateDelete<DeletePayrollRequest>("/payrolls/delete-payroll")
				.AuthenticatedPut<UpdateAdditionalTermsRequest>("/payrolls/{payrollId}/additional-terms", DefaultPermissions.Employees.Write);
        }

		public void DefineServices(IServiceCollection services)
		{
			services.AddTransient<IPayrollsRepository, PayrollsRepository>()
				.AddTransient<IPayrollsService, PayrollsService>();
		}
	}
}
