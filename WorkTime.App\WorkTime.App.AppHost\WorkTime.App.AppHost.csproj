﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
	  <ImplicitUsings>enable</ImplicitUsings>
    <IsAspireHost>true</IsAspireHost>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Aspire.Hosting.AppHost" Version="8.2.2" />
	  <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.11.0" />
	  <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.11.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\gateway\Gateway\Gateway.csproj" />
	<ProjectReference Include="..\..\sso\SSO\SSO.csproj" />
	<ProjectReference Include="..\..\sso\SSO.Database\SSO.Database.csproj" />
	<ProjectReference Include="..\..\WatchTower.Server\WatchTower.Server.csproj" />
	<ProjectReference Include="..\..\WatchTower\watchtowerapi\WatchTower.Api\WatchTower.Api.csproj" />
	<!--<ProjectReference Include="..\..\WatchTower.Server\WatchTower.Server.csproj" />
	<ProjectReference Include="..\..\WatchTower\watchtowerapi\WatchTower.Api\WatchTower.Api.csproj" />-->
	<ProjectReference Include="..\..\worktimeapi\WorkTimeApi\WorkTimeApi.csproj" />
	<ProjectReference Include="..\..\WorkTime\WorkTime.Server\WorkTime.Server.csproj" />
  </ItemGroup>

</Project>
