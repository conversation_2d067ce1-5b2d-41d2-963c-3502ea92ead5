using AutoMapper;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Payrolls;
using WorkTimeApi.Services.Interfaces.Payrolls;

namespace WorkTimeApi.Services.Payrolls
{
    public class PayrollsService : IPayrollsService
    {
        private readonly IPayrollsRepository _payrollsRepository;
        private readonly IMapper _mapper;

        public PayrollsService(IPayrollsRepository payrollsRepository, IMapper mapper)
        {
            _payrollsRepository = payrollsRepository;
            _mapper = mapper;
        }

        public async Task<PayrollDTO> AddPayrollAsync(AddPayrollRequest addPayrollRequest)
        {
            var payroll = _mapper.Map<Payroll>(addPayrollRequest.PayrollDTO);
            var payrollDTO = _mapper.Map<PayrollDTO>(await _payrollsRepository.AddPayrollAsync(payroll));

            return payrollDTO;
        }

        public async Task<IEnumerable<LightPayrollDTO>> LoadLightPayrollsAsync(LoadPayrollsRequest loadPayrollsRequest)
        {
            var payrolls = await _payrollsRepository.GetPayrollsAsync(loadPayrollsRequest.CompanyId);

            var lightPayrollsDTO = (payrolls ?? Enumerable.Empty<Payroll>())
            .Select(payroll =>
            {
                var payrollDTO = _mapper.Map<LightPayrollDTO>(payroll);
                payrollDTO.StructureLevelId = payroll.AnnexPayrolls?
                    .OrderBy(a => a.FromDate ?? DateTime.MinValue)
                    .LastOrDefault()?.StructureLevelId ?? payrollDTO.StructureLevelId;
                return payrollDTO;
            })
            .ToList();

            return lightPayrollsDTO;
        }

        public async Task<PayrollSummaryDTO> LoadPayrollDataAsync(Guid payrollId)
        {
            var payroll = await _payrollsRepository.GetPayrollWithAnnexPayrollsAsync(payrollId);

            if (payroll == null)
                return null;

            var payrollSummary = MapToPayrollSummaryDTO(payroll);

            return payrollSummary;
        }

        public async Task DeletePayrollAsync(DeletePayrollRequest deletePayrollRequest)
        {
            await _payrollsRepository.DeletePayrollAsync(deletePayrollRequest.PayrollId);
        }

        public async Task<PayrollDTO> GetByIdAsync(Guid payrollId)
        {
            var payrolls = await _payrollsRepository.FindAsync(p => p.Id == payrollId);

            return _mapper.Map<PayrollDTO>(payrolls.FirstOrDefault());
        }

        private PayrollSummaryDTO MapToPayrollSummaryDTO(Payroll payroll)
        {
            var payrollSummary = new PayrollSummaryDTO
            {
                ContractNumber = payroll.ContractNumber,
                StructureLevelId = payroll.StructureLevelId,
                StructureLevelName = payroll?.StructureLevel?.Name,
                Position = payroll.Position != null ? new NomenclatureDTO
                {
                    Name = payroll.Position
                } : null,
                ContractType = payroll.ContractType.HasValue ? new NomenclatureDTO
                {
                    Name = Common.Extensions.EnumExtensions.GetDescription(payroll.ContractType.Value),
                    Identifier = payroll.ContractType.Value.ToString()
                } : null,
                ContractReason = payroll.ContractReason.HasValue ? new NomenclatureDTO
                {
                    Name = Common.Extensions.EnumExtensions.GetDescription(payroll.ContractReason.Value),
                    Identifier = payroll.ContractReason.Value.ToString()
                } : null,
                IncomeType = payroll.IncomeType.HasValue ? new NomenclatureDTO
                {
                    Name = Common.Extensions.EnumExtensions.GetDescription(payroll.IncomeType.Value),
                    Identifier = payroll.IncomeType.Value.ToString()
                } : null,
                PermanentContractType = payroll.PermanentContractType.HasValue ? new NomenclatureDTO
                {
                    Name = Common.Extensions.EnumExtensions.GetDescription(payroll.PermanentContractType.Value),
                    Identifier = payroll.PermanentContractType.Value.ToString()
                } : null,
                DailyWorktime = payroll.PayrollWorkTime,
                Workplace = payroll.Location,
                FromDate = payroll.FromDate,
                ToDate = payroll.ToDate,
                ContractDate = payroll.ContractDate,
                ContractEndDate = payroll.ContrractEndDate,
                ContractTermDate = payroll.ContrractTermDate,
                Ekatte = payroll.EKATTECode,
                AdditionalTerms = payroll.AdditionalTerms,
                ProfessionalЕxperienceInCompany = GetLengthOfServiceByType(payroll.Employee.LengthsOfService, (int)LengthOfServiceType.ProfessionalЕxperienceInCompany),
                ProfessionalЕxperience = GetLengthOfServiceByType(payroll.Employee.LengthsOfService, (int)LengthOfServiceType.ProfessionalЕxperience),
                WorkExperience = GetLengthOfServiceByType(payroll.Employee.LengthsOfService, (int)LengthOfServiceType.WorkЕxperience),
                AnnexPayrolls = payroll.AnnexPayrolls.OrderByDescending(a => a.FromDate)?.Select(MapToAnnexPayrollSummary).ToList() ?? new List<AnnexPayrollSummary>()
            };

            var nkpd = payroll.PayrollKPDs?.OrderBy(a => a.NKPD.ValidFrom)?.LastOrDefault();
            var kid = payroll.PayrollKIDs?.OrderBy(a => a.KID.ValidFrom)?.LastOrDefault();

            if (nkpd != null)
                payrollSummary.Nkpd = nkpd?.NKPD.Section + nkpd?.NKPD.Code;
            if (kid != null)
                payrollSummary.Kid = kid?.KID.Section + kid.KID.Code;

            return payrollSummary;
        }

        private AnnexPayrollSummary MapToAnnexPayrollSummary(AnnexPayroll annexPayroll)
        {
            var annexSummary = new AnnexPayrollSummary
            {
                ContractNumber = annexPayroll.ContractNumber,
                StructureLevelId = annexPayroll.StructureLevelId,
                StructureLevelName = annexPayroll?.StructureLevel?.Name,
                AnnexPayrollNumber = annexPayroll.AnnexPayrollNumber,
                Position = annexPayroll.Position != null ? new NomenclatureDTO
                {
                    Name = annexPayroll.Position
                } : null,
                DailyWorktime = annexPayroll.PayrollWorkTime ?? 0,
                Workplace = annexPayroll.Location,
                FromDate = annexPayroll.FromDate,
                ToDate = annexPayroll.AnnexPayrollToDate,
                ContractTermDate = annexPayroll.ContrractTermDate,
                ContractDate = annexPayroll.ContractDate,
                ContractEndDate = annexPayroll.ContrractEndDate,
                Ekatte = annexPayroll.EKATTECode,
                ContractReason = annexPayroll.ContractReason.HasValue ? new NomenclatureDTO
                {
                    Name = Common.Extensions.EnumExtensions.GetDescription(annexPayroll.ContractReason.Value),
                    Identifier = annexPayroll.ContractReason.Value.ToString()
                } : null,
                IncomeType = annexPayroll.IncomeType.HasValue ? new NomenclatureDTO{
                    Name = Common.Extensions.EnumExtensions.GetDescription(annexPayroll.IncomeType.Value),
                    Identifier = annexPayroll.IncomeType.Value.ToString()
                } : null,
            };

            var nkpd = annexPayroll.AnnexPayrollKPDs?.OrderBy(a => a.NKPD.ValidFrom)?.LastOrDefault();
            var kid = annexPayroll.AnnexPayrollKIDs?.OrderBy(a => a.KID.ValidFrom)?.LastOrDefault();

            if (nkpd != null)
                annexSummary.Nkpd = nkpd?.NKPD.Section + nkpd?.NKPD.Code;
            if (kid != null)
                annexSummary.Kid = kid?.KID.Section + kid.KID.Code;

            return annexSummary;

        }

        private LengthOfServiceDTO? GetLengthOfServiceByType(ICollection<LengthOfService> lengthsOfService, int type)
        {
            if (lengthsOfService == null)
                return null;

            var lengthOfService = lengthsOfService?.FirstOrDefault(ls => ls.Type == type);

            if (lengthOfService == null)
                return null;

            return new LengthOfServiceDTO
            {
                Years = lengthOfService.Years,
                Months = lengthOfService.Months,
                Days = lengthOfService.Days,
                Type = lengthOfService.Type,
                Month = lengthOfService.Month
            };
        }

        public async Task<List<PayrollsPositionNamesDTO>?> LoadPayrollsPositionNamesAsync(Guid employeeId)
        {
            var payrolls = await _payrollsRepository.GetPayrollsByEmployeeIdAsync(employeeId);
            return payrolls
                .Select(p => new PayrollsPositionNamesDTO
                {
                    PayrollId = p.Id,
                    PositionName = p.AnnexPayrolls!= null && p.AnnexPayrolls.Any() 
                    ? p?.AnnexPayrolls?.LastOrDefault()?.Position 
                    : p.Position
                })
                .ToList();
        }

        public async Task UpdateAdditionalTermsAsync(UpdateAdditionalTermsRequest updateAdditionalTermsRequest)
        {
            await _payrollsRepository.UpdateAdditionalTermsAsync(updateAdditionalTermsRequest.PayrollId, updateAdditionalTermsRequest.AdditionalTerms);
        }
    }
}
