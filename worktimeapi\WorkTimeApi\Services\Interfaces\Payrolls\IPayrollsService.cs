using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.Requests.Payrolls;

namespace WorkTimeApi.Services.Interfaces.Payrolls
{
	public interface IPayrollsService
	{
		Task<PayrollDTO> AddPayrollAsync(AddPayrollRequest addPayrollRequest);

		Task<IEnumerable<LightPayrollDTO>> LoadLightPayrollsAsync(LoadPayrollsRequest loadPayrollsRequest);

		Task<PayrollSummaryDTO> LoadPayrollDataAsync(Guid payrollId);

		Task DeletePayrollAsync(DeletePayrollRequest deletePayrollRequest);

		Task<PayrollDTO> GetByIdAsync(Guid payrollId);

		Task<List<PayrollsPositionNamesDTO>?> LoadPayrollsPositionNamesAsync(Guid employeeId);

		Task UpdateAdditionalTermsAsync(UpdateAdditionalTermsRequest updateAdditionalTermsRequest);
    }
}
