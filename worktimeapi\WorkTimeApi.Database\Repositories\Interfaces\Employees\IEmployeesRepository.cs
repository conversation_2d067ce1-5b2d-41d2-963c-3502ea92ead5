using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;

namespace WorkTimeApi.Database.Repositories.Interfaces.Employees
{
    public interface IEmployeesRepository : IBaseRepository<Employee>
    {
        Task<Employee> AddEmployeeAsync(Employee employee);

        Task<Employee> AddEmployeeToCompanyAsync(Employee employee, Guid companyId);

        Task<Employee?> GetEmployeeAsync(Guid id);

        Task<bool> CheckIfEmployeeAlreadyExists(Employee employee);

        Task UpdateEmployeeAsync(Employee employee);

        Task<IEnumerable<Employee>> GetEmployeesAsync(Guid companyId);

        Task<IEnumerable<Employee>> GetEmployeeCompaniesAsync(Guid userId, IEnumerable<Guid> companyIds);

        Task<List<Address>> GetEmployeeAddressesAsync(Guid employeeId);

        Task<bool> EditEmployeeAsync(Employee employee);

        Task ImportEmployeeCompaniesAsync(IEnumerable<Guid> employeeIds, Guid companyId);

        Task AddPendingRoleEmployeeAsync(PendingRoleEmployee pendingRoleEmployee);

        Task<IEnumerable<Employee>> GetEmployeePayrollListAsync(Guid companyId);

        Task<IEnumerable<Employee>> GetUserEmployeePayrollListAsync(Guid userId, Guid companyId);

        Task<IEnumerable<Employee>> GetEmployeeAsync(Guid companyId, string permission);

        Task<Employee?> GetUserEmployeeAsync(Guid userId, Guid companyId);

        Task<Employee> AddEmployeeToCompanyAsync(Guid companyId, EmployeeStatus status, Guid userId);

        Task<List<Role>> GetEmployeeRoles(Guid employeeId);

        Task<Employee?> GetUserEmployeePermissionAsync(Guid userId, Guid companyId);

        Task<BankAccount?> GetEmployeeIbanAsync(Guid employeeId, BankAccountPurpose purpose);

        Task<Employee> DoesEmployeeExistInCompanyAsync(string email, string egn, Guid companyId);

        Task<Employee> UpdateUserEmployeeStatusAsync(Guid userId, Guid companyId, EmployeeStatus status);

        Task<Employee> UpdateEmployeeStatusAsync(Guid employeeId, EmployeeStatus status);

        Task<Employee?> GetFullEmployeeAsync(Guid id);

        Task SaveEmployeePropertyEditsAsync(IEnumerable<EmployeePropertyEdit> propertyEdits);

        Task<List<EmployeePropertyEdit>> GetEmployeePropertyEditsAsync(Guid employeeId);

        Task<List<EmployeePropertyEdit>> GetPendingEmployeePropertyEditsAsync(Guid employeeId);

        Task<EmployeePropertyEdit?> GetEmployeePropertyEditByIdAsync(Guid propertyEditId);

        Task UpdateEmployeePropertyEditStatusAsync(Guid propertyEditId, EditStatus newStatus);

        Task UpsertEmployeePropertyEditsAsync(Guid employeeId, IEnumerable<EmployeePropertyEdit> propertyEdits);

        Task UpdateAllEmployeePropertyEditStatusAsync(Guid employeeId, EditStatus newStatus);

        Task DeleteEmployeePropertyEditsAsync(Guid employeeId);

        Task DeleteEmployeePropertyEditAsync(Guid propertyEditId);
    }
}
