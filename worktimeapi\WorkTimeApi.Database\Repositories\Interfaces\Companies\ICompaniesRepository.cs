using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;

namespace WorkTimeApi.Database.Repositories.Interfaces.Companies
{
    public interface ICompaniesRepository : IBaseRepository<Company>
    {
        Task<IEnumerable<Company>> GetActiveCompaniesAsync(Guid employeeId);

        Task<IEnumerable<Company>> GetPendingCompaniesAsync(Guid employeeId);

        Task<Company?> GetCompanyAsync(Guid id);

        Task<Company?> GetCompanyByUserRegistrarionsIdAsync(int userRegistrarionsId);

        Task<Company?> GetCompanyAsync(string email, string bulstat);

        Task<Company> CreateCompanyAsync(Company company, Guid userId, EmployeeStatus status);

        Task<bool> IsEmployeeCompanyExistingAsync(Employee employeeCompany);

        Task<bool> CheckIfCompanyAlreadyExistsAsync(Company company);

        Task<Company> UpdateCompanyAsync(Company company);

        Task<Employee> ShareCompanyAsync(User user, Employee employee, Guid companyId, Guid roleId);

        Task AddCompanyAddressesAsync(IEnumerable<CompanyAddress> companyAddresses);

        Task AddCompanyKidsAsync(IEnumerable<CompanyKid> companyKids);

        Task RemoveCompanyAddressesAsync(Guid companyId);

        Task RemoveCompanyKidsAsync(Guid companyId);

        Task DeleteCompanyAsync(Guid companyId);
    }
}
