﻿using WorkTimeApi.Common.DTOs.Addresses;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Common.DTOs.Employees
{
    public class EmployeeDTO
    {
        public Guid WorkTimeId { get; set; }

        public int? TRZEmployeeId { get; set; }

        public string? FirstName { get; set; }

        public string? SecondName { get; set; }

        public string? LastName { get; set; }

        public string? EGN { get; set; }

        public string? Email { get; set; }

        public string? WorkEmail { get; set; }

        public string? Phone { get; set; }

        public string? WorkPhone { get; set; }

        public string? IDNumber { get; set; }

        public string? IDIssuedFrom { get; set; }

        public Guid CompanyId { get; set; }

        public CompanyDTO? Company { get; set; }

        public Guid UserId { get; set; }

        public DateTime? IDIssueDate { get; set; }

        public DateTime? BirthDate { get; set; }

        public string? BirthPlace { get; set; }

        public bool? IsForeigner { get; set; }

        public Genders Gender { get; set; }

        public string? Citizenship { get; set; }

        public string? Number { get; set; }

        public EducationType Education { get; set; }

        public NomenclatureDTO? EGNType { get; set; }

        public DateTime UploadDate { get; set; }

        public EmployeeStatus Status { get; set; }

        public List<BankAccountDTO>? BankAccounts { get; set; }

        public ICollection<LengthOfServiceDTO> LengthsOfService { get; set; } = [];

        public UserDTO? User { get; set; }

        public ICollection<PayrollDTO> Payrolls { get; set; } = [];

        public ICollection<PayrollDTO> PendingPayrolls { get; set; } = [];

        public ICollection<RoleEmployeeDTO> EmployeeRoles { get; set; } = [];

        public ICollection<AddressDTO> Addresses { get; set; } = [];
    }
}
