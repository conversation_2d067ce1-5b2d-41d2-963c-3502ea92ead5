import styled from "styled-components";
import { useAbsence } from "../../features/absences/AbsenceContext";
import { useAuth } from "../../features/authentication/AuthContext";
import { useMenu } from "../../features/MenuContext";
import { useUserEmployee } from "../../features/UserEmployeeContext";
import { ColorService } from "../../services/ColorService/ColorService";
import { translate } from "../../services/language/Translator";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus";
import { DefaultPermissions } from "../../constants/permissions";
import heart from "../../assets/images/attendancies/heart.svg";
import plane from "../../assets/images/attendancies/airplane.svg";
import approveIcon from "../../assets/images/attendancies/approve.svg";
import declineIcon from "../../assets/images/attendancies/delete.svg";
import { useAbsenceActions } from "../../utils/absenceActions";
import Container from "../Container";
import Avatar, { getAbsenceAvatarColor } from "./Avatar";
import { AbsenceInfo } from "./types/AbsenceInfo.ts";
import { AlignmentPosition } from "./types/AlignmentPosition.ts";
import { Employee } from "../../features/attendance/useFilteredEmployees";
import EmployeeNameWrapper from "./EmployeeNameWrapper .tsx";

const StyledAvatar = styled(Avatar)``;

const ImageButton = styled.label`
  margin-left: auto;
  margin-right: 0.1rem;
  width: 12px;
  height: 12px;
  padding: 0.2rem;
  font-size: 0.7rem;
  font-weight: bold;
  border-radius: 50%;
  display: flex;
  align-items: center;
  min-width: 0;
  gap: 0.2rem;
  justify-content: center;
  color: white;
  background: #f6dce4;
  cursor: pointer;
  opacity: 1;
  &:hover {
    background: #eb7b99;
  }
`;

const StyledDayBox = styled(Container)<{ $numberOfRows: number }>`
  position: relative;
  display: grid;
  grid-template-rows: repeat(${(props) => props.$numberOfRows}, 1.4rem);
  align-content: end;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
  overflow: hidden;
  bottom: 0;
  z-index: 99;
  background-color: transparent;
  cursor: pointer;
  user-select: none;
`;

const LineWrapper = styled(Container)<{
  $row: number;
  $numberOfRows: number;
  $isOtherUser: boolean;
  $isAdmin: boolean;
  $showMyAbsences: boolean;
  $isSelectedEmployee: boolean;
  $hasSelectedEmployee: boolean;
}>`
  position: relative;
  grid-row-start: ${(props) => props.$numberOfRows - props.$row + 1};
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-end;
  opacity: ${(props) =>
    props.$showMyAbsences && props.$isOtherUser
      ? props.$isAdmin
        ? 0.3
        : 0
      : props.$isSelectedEmployee
      ? 1
      : props.$hasSelectedEmployee && !props.$isSelectedEmployee
      ? 0.3
      : 1};
  transition: opacity 0.3s ease;
`;

const StyledLine = styled(Container)<{
  $bgcolor: string;
  $numberOfRows: number;
  $alignmentPosition: AlignmentPosition;
  $isSelected: boolean;
  $isHovered: boolean;
  $isHighlighted?: boolean;
}>`
  position: relative;
  width: 100%;
  height: ${(props) => {
    if (props.$numberOfRows === 1) {
      return "1.3rem";
    } else {
      return "1.3rem";
    }
  }};
  border-radius: ${(props) => {
    switch (props.$alignmentPosition) {
      case AlignmentPosition.Left:
        return "1.5em 0 0 1.5em";
      case AlignmentPosition.Right:
        return "0 1.5em 1.5em 0";
      case AlignmentPosition.Both:
        return "1.5rem 1.5em 1.5em 1.5rem";
      default:
        return "0";
    }
  }};
  background-color: ${(props) => {
    const rgb = ColorService.hexToRgb(props.$bgcolor);
    if (props.$isHovered) {
      return `rgba(${rgb}, 0.99)`;
    }
    if (props.$isSelected) {
      return `rgba(${rgb}, 0.75)`;
    }
    return `rgba(${rgb}, 0.45)`;
  }};
  transition: background-color 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
  border: ${(props) => {
    if (!props.$isHighlighted) return "none";
    switch (props.$alignmentPosition) {
      case AlignmentPosition.Left:
      case AlignmentPosition.Right:
      case AlignmentPosition.Center:
      case AlignmentPosition.Both:
        return "2px solid #ff9800";
      default:
        return "none";
    }
  }};
  border-left: ${(props) =>
    props.$isHighlighted && props.$alignmentPosition === AlignmentPosition.Left
      ? "2px solid #ff9800"
      : "none"};
  border-right: ${(props) =>
    props.$isHighlighted && props.$alignmentPosition === AlignmentPosition.Right
      ? "2px solid #ff9800"
      : props.$isHighlighted &&
        props.$alignmentPosition === AlignmentPosition.Both
      ? "2px solid #ff9800"
      : "none"};
  border-top: ${(props) =>
    props.$isHighlighted &&
    (props.$alignmentPosition === AlignmentPosition.Left ||
      props.$alignmentPosition === AlignmentPosition.Right ||
      props.$alignmentPosition === AlignmentPosition.Center ||
      props.$alignmentPosition === AlignmentPosition.Both)
      ? "2px solid #ff9800"
      : "none"};
  border-bottom: ${(props) =>
    props.$isHighlighted &&
    (props.$alignmentPosition === AlignmentPosition.Left ||
      props.$alignmentPosition === AlignmentPosition.Right ||
      props.$alignmentPosition === AlignmentPosition.Center ||
      props.$alignmentPosition === AlignmentPosition.Both)
      ? "2px solid #ff9800"
      : "none"};
`;

const ButtonGroup = styled(Container)`
  display: flex;
  margin-left: auto;
`;

interface DayBoxProps {
  dayDate: Date;
  dayData: AbsenceInfo[];
  numberOfRows: number;
  selectedEmployee?: Employee;
  hoveredEmployee?: Employee;
  showMyAbsences: boolean;
}

const getAbsenceColor = (absence: AbsenceInfo) => {
  if (
    absence.status === AbsenceStatus.Pending ||
    absence.status === AbsenceStatus.EditedByEmployee ||
    absence.status === AbsenceStatus.DeletedByUserAfterApproval
  )
    return "#FFE9F0";
  if (absence.isHospital) return "#e1effe";
  return "#E0F8E1";
};

const DayBox: React.FC<DayBoxProps> = ({
  dayDate,
  dayData,
  numberOfRows,
  selectedEmployee,
  hoveredEmployee,
  showMyAbsences,
}) => {
  const { user } = useAuth();
  const { userEmployee } = useUserEmployee();
  const { toggleMenu, changeView, isOpen } = useMenu();
  const { setSelectedAbsence, setIsEditing } = useAbsence();
  const { handleApproveAbsence, handleDeclineAbsence, handleDeleteAbsence } =
    useAbsenceActions();

  const sortedDayData = [...dayData].sort(
    (a, b) => (a.row || 0) - (b.row || 0)
  );

  const handleAbsenceClick = (absence: AbsenceInfo) => {
    setSelectedAbsence(absence);
    const isMine = absence.userId === user.userId;
    const isPending = absence.status === AbsenceStatus.Pending;
    setIsEditing(isMine && isPending);

    changeView("absence", "other", {
      selectedYear: dayDate.getFullYear(),
      selectedMonth: dayDate.getMonth(),
    });

    if (!isOpen) {
      toggleMenu();
    }
  };

  return (
    <>
      <StyledDayBox $numberOfRows={numberOfRows - 1} data-testid="day-box">
        {sortedDayData.map((absence) => (
          <LineWrapper
            key={`${absence.payrollId}-${absence.id}-${dayDate.getTime()}`}
            $row={absence.row}
            $numberOfRows={numberOfRows - 1}
            $isOtherUser={absence.userId !== user.userId}
            $isAdmin={userEmployee.permissions.includes(
              DefaultPermissions.Attendances.Write
            )}
            $showMyAbsences={showMyAbsences}
            $isSelectedEmployee={selectedEmployee?.userId === absence.userId}
            $hasSelectedEmployee={!!selectedEmployee}
          >
            <StyledLine
              $bgcolor={getAbsenceColor(absence)}
              $numberOfRows={numberOfRows - 1}
              $alignmentPosition={absence.positonRounding}
              $isSelected={selectedEmployee?.userId === absence.userId}
              $isHovered={hoveredEmployee?.userId === absence.userId}
              $isHighlighted={absence.isHighlighted}
              data-testid={`employee-line-${absence.payrollId}`}
              onClick={() => handleAbsenceClick(absence)}
            >
              <StyledAvatar
                background={getAbsenceAvatarColor(
                  absence.status,
                  absence.isHospital
                )}
                name={absence.employeeName}
                photo={absence.isHospital ? heart : plane}
                size={1.4}
                style={{ marginLeft: "0rem", padding: "0.2rem" }}
                isVisible={
                  absence.positonRounding == AlignmentPosition.Left ||
                  absence.positonRounding == AlignmentPosition.Both
                }
                data-testid={`employee-avatar-${absence.payrollId}`}
              />
              {absence.positonRounding == AlignmentPosition.Left ||
              absence.positonRounding == AlignmentPosition.Both ||
              dayDate?.getDay() === 1 ? (
                !showMyAbsences ||
                absence.status == AbsenceStatus.Approved ||
                absence.status == AbsenceStatus.EditedByAdmin ||
                absence.userId !== user.userId ? (
                  <EmployeeNameWrapper
                    name={absence.employeeName}
                    isDeleted={
                      absence.status ===
                      AbsenceStatus.DeletedByUserAfterApproval
                    }
                  ></EmployeeNameWrapper>
                ) : (
                  <EmployeeNameWrapper
                    name={translate("strAbsenceWaitingForApproval")}
                    isDeleted={
                      absence.status ===
                      AbsenceStatus.DeletedByUserAfterApproval
                    }
                  ></EmployeeNameWrapper>
                )
              ) : null}
              {(absence.positonRounding == AlignmentPosition.Right ||
                absence.positonRounding == AlignmentPosition.Both) &&
                absence.status != AbsenceStatus.Approved &&
                absence.status != AbsenceStatus.EditedByAdmin &&
                absence.userId === user.userId &&
                !userEmployee.permissions.includes(
                  DefaultPermissions.Attendances.Write
                ) && (
                  // Това е личния отпуск на човека
                  <ImageButton
                    onClick={() => handleDeleteAbsence(absence)}
                    data-testid={`delete-absence-${absence.payrollId}`}
                  >
                    <img
                      src={declineIcon}
                      alt="Decline"
                      width={12}
                      height={10}
                    />
                  </ImageButton>
                )}
              {/* Това е отпуск на друг човек, докато имаш права */}
              {(absence.positonRounding == AlignmentPosition.Right ||
                absence.positonRounding == AlignmentPosition.Both) &&
                absence.status != AbsenceStatus.Approved &&
                absence.status != AbsenceStatus.EditedByAdmin &&
                userEmployee.permissions.includes(
                  DefaultPermissions.Attendances.Write
                ) && (
                  <ButtonGroup>
                    <ImageButton
                      onClick={() => handleApproveAbsence(absence)}
                      data-testid={`approve-absence-${absence.payrollId}`}
                    >
                      <img
                        src={approveIcon}
                        alt="Approve"
                        width={12}
                        height={10}
                      />
                    </ImageButton>
                    {(!absence.isHospital ||
                      absence.status ===
                        AbsenceStatus.DeletedByUserAfterApproval) && (
                      <ImageButton
                        onClick={() => handleDeclineAbsence(absence)}
                        data-testid={`decline-absence-${absence.payrollId}`}
                      >
                        <img
                          src={declineIcon}
                          alt="Decline"
                          width={12}
                          height={10}
                        />
                      </ImageButton>
                    )}
                  </ButtonGroup>
                )}
            </StyledLine>
          </LineWrapper>
        ))}
      </StyledDayBox>
    </>
  );
};

export default DayBox;
