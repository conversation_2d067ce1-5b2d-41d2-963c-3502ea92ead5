using Microsoft.EntityFrameworkCore;
using WorkTimeApi.Common;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Users;

namespace WorkTimeApi.Database.Repositories.Users
{
    public class UserRepository(IDbContextFactory<WorkTimeApiDbContext> dbContextFactory)
        : BaseRepository<User>(dbContextFactory), IUserRepository
    {
        public async Task<User?> GetUserAsync(Guid id)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            return await context.Users.FindAsync(id);
        }

        public async Task<User> AddUserAsync(User user)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingUser = await context.Users.Include(u=>u.WorkTimeRole).FirstOrDefaultAsync(c => c.Id == user.Id);
            if (existingUser is not null)
                return existingUser;

            user.HasSignedIn = user.CodePassword == null;
            user.WorkTimeRoleId = new Guid(DefaultWorktimeRole.Employee);

            var pendingRoles = context.PendingRoleEmployees.Where(pr => pr.Email == user.Email);
            if (pendingRoles != null && pendingRoles.Any())
            {
                foreach (var pendingRole in pendingRoles)
                {
                    var employee = new Employee
                    {
                        Email = user.Email,
                        CompanyId = pendingRole.CompanyId,
                        FirstName = user.FirstName,
                        SecondName = user.SecondName,
                        LastName = user.LastName,
                        Status = Common.Enums.EmployeeStatus.UserToApprove,
                        UserId = user.Id
                    };
                    employee.EmployeeRoles.Add(new RoleEmployee { EmployeeId = employee.Id, RoleId = pendingRole.RoleId });

                    context.Employees.Add(employee);
                }

                context.PendingRoleEmployees.RemoveRange(pendingRoles);
            }

            context.Users.Add(user);
            await context.SaveChangesAsync();

            return user;
        }

        public async Task<User> UpdateUserHasSignedInAsync(User user)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            var existingUser = await context.Users.FirstOrDefaultAsync(c => c.Id == user.Id);
            user.HasSignedIn = user.CodePassword == null;

            if (existingUser is not null)
            {
                UpdateUser(user, existingUser);
                await context.SaveChangesAsync();
            }

            return user;
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();

            context.Users.Update(user);
            await context.SaveChangesAsync();

            return user;
        }

        private void UpdateUser(User user, User existingUser)
        {
            existingUser.FirstName = user.FirstName;
            existingUser.SecondName = user.SecondName;
            existingUser.LastName = user.LastName;
            existingUser.Email = user.Email;
            existingUser.Code = user.Code;
            existingUser.CodePassword = user.CodePassword;
            existingUser.HasSignedIn = user.HasSignedIn;
            existingUser.UploadDate = DateTime.UtcNow;
        }

        public async Task<User?> GetEmployeeUserAsync(Guid employeeId)
        {
            using var context = await dbContextFactory.CreateDbContextAsync();
            var employee = await context.Employees.FirstOrDefaultAsync(e => e.Id == employeeId);

            return await context.Users.FirstOrDefaultAsync(u=>u.Id == employee.UserId);
        }
    }
}
