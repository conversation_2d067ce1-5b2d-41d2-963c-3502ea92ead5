﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using Gateway.Common.Globals;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.Roles;
using WorkTimeApi.Connections;
using WorkTimeApi.Connections.Interfaces;
using WorkTimeApi.Database.Repositories.BankAccounts;
using WorkTimeApi.Database.Repositories.Employees;
using WorkTimeApi.Database.Repositories.Interfaces.BankAccounts;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Extensions.MediatorExtensions;
using WorkTimeApi.Mappers;
using WorkTimeApi.Mappers.Interfaces;
using WorkTimeApi.Services.Employees;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.PropertyTracking;
using WorkTimeApi.Services.PropertyTracking;
using WorkTimeApi.Validators;
using WorkTimeApi.Validators.Interfaces;

namespace WorkTimeApi.EndpointDefinitions.Employees
{
    public class EmployeesEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.MediatePost<AddEmployeeRequest>("/employees")
                .MediatePost<CreateEmployeeForCompanyRequest>("/companies/{companyId}/employees")
                .MediateGet<LoadEmployeesRequest>("/employees")
                .AuthenticatedGet<LoadEmployeeRequest>("/employee")
                .AuthenticatedPut<EditEmployeeRequest>("/employee")
                .MediateGet<EmployeesByPermissionRequest>("employees/permission")
                .MediateGet<GetEmployeesByCompanyIdAndPermissionRequest>("emplyoees/company-and-permission")
                .AuthenticatedGet<LoadEmployeePayrollRequest>("/employee-payrolls")
                .MediateGet<LoadCoworkersRequest>("employees/coworkers")
                .MediatePost<AddNewEmployeePayrollRequest>("/employee-payroll")
                .AuthenticatedGet<DoesEmployeeExistInCompanyRequest>("/employee-exists")
                .AuthenticatedPost<ApproveEmployeePropertyEditRequest>("/employees/property-edits/approve")
                .AuthenticatedPost<DeclineEmployeePropertyEditRequest>("/employees/property-edits/decline")
                .AuthenticatedDelete<DeleteEmployeePropertyEditsRequest>("/employees/{EmployeeId}/property-edits")
                .AuthenticatedDelete<DeleteEmployeePropertyEditRequest>("/employees/property-edits/{PropertyEditId}")
                .AuthenticatedPost<AddEmployeeAddressRequest>("/employee/address")
                .AuthenticatedDelete<DeleteEmployeeAddressRequest>("/employee/{EmployeeId}/addresses/{AddressId}")
                .AuthenticatedPost<DeclinePendingEmployeeRequest>("/employees/decline");
        }

        public void DefineServices(IServiceCollection services)
        {
            services.AddScoped<IEmployeesService, EmployeesService>()
                .AddScoped<IEmployeePayrollService, EmployeePayrollService>()
                .AddScoped<IEmployeesListMapper, EmployeesListMapper>()
                .AddScoped<IEmployeesRepository, EmployeesRepository>()
                .AddScoped<IBankAccountsRepository, BankAccountsRepository>()
                .AddScoped<IEmployeeValidator, EmployeeValidator>()
                .AddScoped<GlobalUser>()
                .AddScoped<GlobalEmployee>()
                .AddScoped<IPropertyChangeDetectionService, PropertyChangeDetectionService>();

            services.AddHttpClient<ISSOConnection, SSOConnection>();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        }
    }
}
